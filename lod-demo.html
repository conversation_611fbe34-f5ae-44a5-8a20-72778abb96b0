<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Splatter.app Viewer - LOD & Spatial Chunking Demo</title>
    
    <!-- Vue.js 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #fff;
            overflow: hidden;
        }
        
        .demo-container {
            display: flex;
            height: 100vh;
        }
        
        .viewport {
            flex: 1;
            position: relative;
            background: linear-gradient(45deg, #2a2a2a, #1a1a1a);
        }
        
        .viewport canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .controls-panel {
            width: 350px;
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            overflow-y: auto;
            border-left: 1px solid #333;
        }
        
        .control-section {
            margin-bottom: 25px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .control-section h3 {
            margin-bottom: 15px;
            color: #4CAF50;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
        }
        
        .control-group input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .stat-label {
            font-size: 10px;
            opacity: 0.7;
            text-transform: uppercase;
        }
        
        .lod-levels {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .lod-level {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }
        
        .lod-level.active {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        
        .performance-meter {
            height: 60px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        
        .performance-bar {
            height: 100%;
            background: linear-gradient(to right, #4CAF50, #FFC107, #F44336);
            transition: width 0.3s ease;
        }
        
        .performance-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }
        
        .block-visualization {
            height: 200px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        
        .block {
            position: absolute;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            transition: all 0.3s ease;
        }
        
        .block.lod-0 { background: rgba(76, 175, 80, 0.6); }
        .block.lod-1 { background: rgba(255, 193, 7, 0.6); }
        .block.lod-2 { background: rgba(255, 152, 0, 0.6); }
        .block.lod-3 { background: rgba(244, 67, 54, 0.6); }
        .block.unloaded { background: rgba(128, 128, 128, 0.3); }
        
        .camera-indicator {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #2196F3;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }
        
        .info-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin: 2px;
            transition: all 0.2s;
        }
        
        .btn-primary { background: #2196F3; color: white; }
        .btn-success { background: #4CAF50; color: white; }
        .btn-warning { background: #FF9800; color: white; }
        .btn-danger { background: #F44336; color: white; }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }
        
        @media (max-width: 768px) {
            .demo-container {
                flex-direction: column;
            }
            
            .controls-panel {
                width: 100%;
                height: 300px;
                border-left: none;
                border-top: 1px solid #333;
            }
        }
    </style>
</head>
<body>
    <div id="app" class="demo-container">
        <!-- 主视口 -->
        <div class="viewport">
            <canvas ref="canvas"></canvas>
            
            <!-- 信息覆盖层 -->
            <div class="info-overlay">
                <div>FPS: {{ stats.performance.fps.toFixed(1) }}</div>
                <div>Frame Time: {{ stats.performance.frameTime.toFixed(1) }}ms</div>
                <div>Memory: {{ (stats.performance.memoryUsage / 1024 / 1024).toFixed(1) }}MB</div>
                <div>Visible Blocks: {{ stats.spatial.visibleBlocks }}</div>
                <div>Loaded Blocks: {{ stats.spatial.loadedBlocks }}</div>
                <div>LOD Bias: {{ lodSettings.bias.toFixed(1) }}</div>
            </div>
            
            <!-- 块可视化 -->
            <div class="block-visualization" style="position: absolute; bottom: 20px; right: 20px; width: 200px;">
                <div class="camera-indicator" 
                     :style="{ left: cameraPosition.x + '%', top: cameraPosition.y + '%' }"></div>
                <div v-for="block in visualBlocks" 
                     :key="block.id"
                     class="block"
                     :class="`lod-${block.lodLevel}`"
                     :style="{
                         left: block.x + '%',
                         top: block.y + '%',
                         width: block.size + '%',
                         height: block.size + '%'
                     }"></div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="controls-panel">
            <!-- LOD控制 -->
            <div class="control-section">
                <h3>Level of Detail (LOD)</h3>
                
                <div class="control-group">
                    <label>LOD Bias: {{ lodSettings.bias.toFixed(1) }}</label>
                    <input type="range" 
                           v-model.number="lodSettings.bias" 
                           min="-2" max="2" step="0.1"
                           @input="updateLODSettings">
                    <small>Negative: Higher quality, Positive: Lower quality</small>
                </div>
                
                <div class="control-group">
                    <label>
                        <input type="checkbox" 
                               v-model="lodSettings.adaptive"
                               @change="updateLODSettings">
                        Adaptive LOD
                    </label>
                    <small>Automatically adjust quality based on performance</small>
                </div>
                
                <div class="control-group">
                    <label>Target FPS: {{ lodSettings.targetFPS }}</label>
                    <input type="range" 
                           v-model.number="lodSettings.targetFPS" 
                           min="30" max="120" step="5"
                           @input="updateLODSettings">
                </div>
                
                <!-- LOD级别显示 -->
                <div class="lod-levels">
                    <div v-for="(level, index) in lodLevels" 
                         :key="index"
                         class="lod-level"
                         :class="{ active: level.activeBlocks > 0 }">
                        <span>LOD {{ index }}</span>
                        <span>{{ level.activeBlocks }} blocks</span>
                    </div>
                </div>
            </div>
            
            <!-- 空间管理 -->
            <div class="control-section">
                <h3>Spatial Management</h3>
                
                <div class="control-group">
                    <label>Loading Radius: {{ spatialSettings.loadingRadius.toFixed(1) }}</label>
                    <input type="range" 
                           v-model.number="spatialSettings.loadingRadius" 
                           min="10" max="200" step="5"
                           @input="updateSpatialSettings">
                </div>
                
                <div class="control-group">
                    <label>Max Concurrent Loads: {{ spatialSettings.maxConcurrentLoads }}</label>
                    <input type="range" 
                           v-model.number="spatialSettings.maxConcurrentLoads" 
                           min="2" max="16" step="1"
                           @input="updateSpatialSettings">
                </div>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.spatial.totalBlocks }}</div>
                        <div class="stat-label">Total Blocks</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.spatial.visibleBlocks }}</div>
                        <div class="stat-label">Visible</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.spatial.loadedBlocks }}</div>
                        <div class="stat-label">Loaded</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.spatial.cullingEfficiency.toFixed(1) }}%</div>
                        <div class="stat-label">Culling Eff.</div>
                    </div>
                </div>
            </div>
            
            <!-- 性能监控 -->
            <div class="control-section">
                <h3>Performance</h3>
                
                <div class="performance-meter">
                    <div class="performance-bar" 
                         :style="{ width: performancePercentage + '%' }"></div>
                    <div class="performance-text">
                        {{ stats.performance.fps.toFixed(0) }} FPS
                    </div>
                </div>
                
                <div class="stats-grid" style="margin-top: 15px;">
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.performance.frameTime.toFixed(1) }}</div>
                        <div class="stat-label">Frame Time (ms)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ (stats.performance.memoryUsage / 1024 / 1024).toFixed(0) }}</div>
                        <div class="stat-label">Memory (MB)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.performance.renderTime.toFixed(1) }}</div>
                        <div class="stat-label">Render Time (ms)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.performance.cullingTime.toFixed(1) }}</div>
                        <div class="stat-label">Culling Time (ms)</div>
                    </div>
                </div>
            </div>
            
            <!-- 控制按钮 -->
            <div class="control-section">
                <h3>Controls</h3>
                
                <button class="btn btn-primary" @click="resetCamera">Reset Camera</button>
                <button class="btn btn-success" @click="optimizeSettings">Auto Optimize</button>
                <button class="btn btn-warning" @click="clearCache">Clear Cache</button>
                <button class="btn btn-danger" @click="resetStats">Reset Stats</button>
                
                <div style="margin-top: 15px;">
                    <label>
                        <input type="checkbox" v-model="debugMode">
                        Debug Mode
                    </label>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        const { createApp, ref, reactive, computed, onMounted, onUnmounted } = Vue;
        
        createApp({
            setup() {
                // 响应式数据
                const canvas = ref(null);
                const debugMode = ref(false);
                
                const lodSettings = reactive({
                    bias: 0,
                    adaptive: true,
                    targetFPS: 60
                });
                
                const spatialSettings = reactive({
                    loadingRadius: 50,
                    maxConcurrentLoads: 6
                });
                
                const stats = reactive({
                    performance: {
                        fps: 60,
                        frameTime: 16.67,
                        memoryUsage: 256 * 1024 * 1024,
                        renderTime: 8.5,
                        cullingTime: 1.2
                    },
                    spatial: {
                        totalBlocks: 1000,
                        visibleBlocks: 150,
                        loadedBlocks: 200,
                        cullingEfficiency: 85.0
                    }
                });
                
                const lodLevels = reactive([
                    { level: 0, activeBlocks: 45, threshold: 10 },
                    { level: 1, activeBlocks: 65, threshold: 30 },
                    { level: 2, activeBlocks: 35, threshold: 80 },
                    { level: 3, activeBlocks: 5, threshold: 200 }
                ]);
                
                const cameraPosition = reactive({ x: 50, y: 50 });
                const visualBlocks = ref([]);
                
                // 计算属性
                const performancePercentage = computed(() => {
                    return Math.min(100, (stats.performance.fps / lodSettings.targetFPS) * 100);
                });
                
                // 模拟数据更新
                let animationId;
                let lastTime = 0;
                
                const updateSimulation = (currentTime) => {
                    const deltaTime = currentTime - lastTime;
                    lastTime = currentTime;
                    
                    // 模拟性能波动
                    stats.performance.fps = 60 + Math.sin(currentTime * 0.001) * 10;
                    stats.performance.frameTime = 1000 / stats.performance.fps;
                    stats.performance.memoryUsage += Math.random() * 1024 * 1024 - 512 * 1024;
                    stats.performance.memoryUsage = Math.max(128 * 1024 * 1024, stats.performance.memoryUsage);
                    
                    // 模拟相机移动
                    cameraPosition.x = 50 + Math.sin(currentTime * 0.0005) * 30;
                    cameraPosition.y = 50 + Math.cos(currentTime * 0.0007) * 30;
                    
                    // 更新可见块统计
                    const baseVisible = 150;
                    stats.spatial.visibleBlocks = baseVisible + Math.floor(Math.sin(currentTime * 0.001) * 50);
                    
                    // 根据LOD偏移调整块分布
                    const biasEffect = lodSettings.bias;
                    lodLevels[0].activeBlocks = Math.max(0, 45 - biasEffect * 10);
                    lodLevels[1].activeBlocks = Math.max(0, 65 - biasEffect * 5);
                    lodLevels[2].activeBlocks = Math.max(0, 35 + biasEffect * 5);
                    lodLevels[3].activeBlocks = Math.max(0, 5 + biasEffect * 10);
                    
                    // 生成可视化块
                    generateVisualBlocks();
                    
                    animationId = requestAnimationFrame(updateSimulation);
                };
                
                const generateVisualBlocks = () => {
                    const blocks = [];
                    const gridSize = 10;
                    
                    for (let i = 0; i < gridSize; i++) {
                        for (let j = 0; j < gridSize; j++) {
                            const x = (i / gridSize) * 100;
                            const y = (j / gridSize) * 100;
                            
                            // 计算到相机的距离
                            const dx = x - cameraPosition.x;
                            const dy = y - cameraPosition.y;
                            const distance = Math.sqrt(dx * dx + dy * dy);
                            
                            // 确定LOD级别
                            let lodLevel = 3;
                            if (distance < 20) lodLevel = 0;
                            else if (distance < 35) lodLevel = 1;
                            else if (distance < 50) lodLevel = 2;
                            
                            // 应用LOD偏移
                            lodLevel = Math.min(3, Math.max(0, lodLevel + Math.floor(lodSettings.bias)));
                            
                            blocks.push({
                                id: i * gridSize + j,
                                x: x,
                                y: y,
                                size: 8,
                                lodLevel: lodLevel,
                                distance: distance
                            });
                        }
                    }
                    
                    visualBlocks.value = blocks;
                };
                
                // 方法
                const updateLODSettings = () => {
                    console.log('LOD settings updated:', lodSettings);
                };
                
                const updateSpatialSettings = () => {
                    console.log('Spatial settings updated:', spatialSettings);
                };
                
                const resetCamera = () => {
                    cameraPosition.x = 50;
                    cameraPosition.y = 50;
                };
                
                const optimizeSettings = () => {
                    if (stats.performance.fps < 45) {
                        lodSettings.bias = Math.min(2, lodSettings.bias + 0.5);
                        spatialSettings.maxConcurrentLoads = Math.max(2, spatialSettings.maxConcurrentLoads - 1);
                    } else if (stats.performance.fps > 70) {
                        lodSettings.bias = Math.max(-2, lodSettings.bias - 0.5);
                        spatialSettings.maxConcurrentLoads = Math.min(16, spatialSettings.maxConcurrentLoads + 1);
                    }
                };
                
                const clearCache = () => {
                    stats.spatial.loadedBlocks = 0;
                    setTimeout(() => {
                        stats.spatial.loadedBlocks = stats.spatial.visibleBlocks;
                    }, 1000);
                };
                
                const resetStats = () => {
                    stats.performance.memoryUsage = 256 * 1024 * 1024;
                    lodSettings.bias = 0;
                    spatialSettings.loadingRadius = 50;
                    spatialSettings.maxConcurrentLoads = 6;
                };
                
                // 生命周期
                onMounted(() => {
                    // 初始化WebGL上下文
                    const gl = canvas.value.getContext('webgl2');
                    if (gl) {
                        gl.clearColor(0.1, 0.1, 0.1, 1.0);
                        
                        const render = () => {
                            gl.clear(gl.COLOR_BUFFER_BIT);
                        };
                        
                        render();
                    }
                    
                    // 开始模拟
                    animationId = requestAnimationFrame(updateSimulation);
                    
                    // 初始化可视化块
                    generateVisualBlocks();
                });
                
                onUnmounted(() => {
                    if (animationId) {
                        cancelAnimationFrame(animationId);
                    }
                });
                
                return {
                    canvas,
                    debugMode,
                    lodSettings,
                    spatialSettings,
                    stats,
                    lodLevels,
                    cameraPosition,
                    visualBlocks,
                    performancePercentage,
                    updateLODSettings,
                    updateSpatialSettings,
                    resetCamera,
                    optimizeSettings,
                    clearCache,
                    resetStats
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
