<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Splatter.app - True LOD System Demo</title>
    
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #fff;
            overflow: hidden;
        }
        
        .demo-container {
            display: flex;
            height: 100vh;
        }
        
        .viewport {
            flex: 1;
            position: relative;
            background: linear-gradient(45deg, #2a2a2a, #1a1a1a);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .scene-visualization {
            width: 500px;
            height: 500px;
            border: 2px solid #333;
            position: relative;
            background: radial-gradient(circle, #333 1px, transparent 1px);
            background-size: 25px 25px;
        }
        
        .block {
            position: absolute;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 9px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .block.lod-0 { background: rgba(76, 175, 80, 0.9); color: white; } /* 高细节 */
        .block.lod-1 { background: rgba(255, 193, 7, 0.8); color: black; } /* 中等细节 */
        .block.lod-2 { background: rgba(255, 152, 0, 0.7); color: white; } /* 低细节 */
        .block.lod-3 { background: rgba(244, 67, 54, 0.6); color: white; } /* 最低细节 */
        .block.culled { background: rgba(128, 128, 128, 0.3); color: #666; } /* 剔除 */
        .block.loading { 
            background: rgba(33, 150, 243, 0.8); 
            color: white;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }
        
        .camera {
            position: absolute;
            width: 16px;
            height: 16px;
            background: #FF5722;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            box-shadow: 0 0 15px rgba(255, 87, 34, 0.8);
        }
        
        .camera::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-left: 8px solid #FF5722;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
            transform: translate(-50%, -50%) rotate(var(--camera-angle, 0deg));
        }
        
        .lod-radius {
            position: absolute;
            border: 2px dashed;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
        }
        
        .lod-0-radius { border-color: rgba(76, 175, 80, 0.6); }
        .lod-1-radius { border-color: rgba(255, 193, 7, 0.6); }
        .lod-2-radius { border-color: rgba(255, 152, 0, 0.6); }
        
        .controls-panel {
            width: 350px;
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            overflow-y: auto;
        }
        
        .control-section {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        
        .control-section h3 {
            margin-bottom: 10px;
            color: #4CAF50;
            font-size: 14px;
        }
        
        .detail-control {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        
        .detail-value {
            font-size: 18px;
            font-weight: bold;
            color: #4CAF50;
            min-width: 60px;
        }
        
        .lod-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-top: 10px;
        }
        
        .lod-stat {
            background: rgba(255, 255, 255, 0.05);
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
        
        .lod-stat.lod-0 { border-left: 3px solid #4CAF50; }
        .lod-stat.lod-1 { border-left: 3px solid #FFC107; }
        .lod-stat.lod-2 { border-left: 3px solid #FF9800; }
        .lod-stat.culled { border-left: 3px solid #9E9E9E; }
        
        .stat-value {
            font-size: 16px;
            font-weight: bold;
        }
        
        .stat-label {
            font-size: 10px;
            opacity: 0.7;
        }
        
        .btn {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin: 2px;
            transition: all 0.2s;
        }
        
        .btn-primary { background: #2196F3; color: white; }
        .btn-success { background: #4CAF50; color: white; }
        .btn-warning { background: #FF9800; color: white; }
        .btn-danger { background: #F44336; color: white; }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }
        
        input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .performance-meter {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
        }
        
        .fps-display {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
        }
        
        .fps-good { color: #4CAF50; }
        .fps-medium { color: #FFC107; }
        .fps-poor { color: #F44336; }
        
        .memory-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .memory-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #FFC107, #F44336);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div id="app" class="demo-container">
        <!-- 主视口 -->
        <div class="viewport">
            <div class="scene-visualization">
                <!-- LOD半径指示器 -->
                <div class="lod-radius lod-0-radius" 
                     :style="{
                         left: camera.x + '%',
                         top: camera.y + '%',
                         width: lodRadii.lod0 * 2 + 'px',
                         height: lodRadii.lod0 * 2 + 'px'
                     }"></div>
                
                <div class="lod-radius lod-1-radius" 
                     :style="{
                         left: camera.x + '%',
                         top: camera.y + '%',
                         width: lodRadii.lod1 * 2 + 'px',
                         height: lodRadii.lod1 * 2 + 'px'
                     }"></div>
                
                <div class="lod-radius lod-2-radius" 
                     :style="{
                         left: camera.x + '%',
                         top: camera.y + '%',
                         width: lodRadii.lod2 * 2 + 'px',
                         height: lodRadii.lod2 * 2 + 'px'
                     }"></div>
                
                <!-- 相机 -->
                <div class="camera" 
                     :style="{ 
                         left: camera.x + '%', 
                         top: camera.y + '%',
                         '--camera-angle': camera.angle + 'deg'
                     }"></div>
                
                <!-- 数据块 -->
                <div v-for="block in blocks" 
                     :key="block.id"
                     class="block"
                     :class="[block.lodClass, { loading: block.loading }]"
                     :style="{
                         left: block.x + '%',
                         top: block.y + '%',
                         width: block.size + 'px',
                         height: block.size + 'px'
                     }"
                     :title="`Block ${block.id} - LOD: ${block.lodLevel} - Distance: ${block.distance.toFixed(1)}`"
                     @click="selectBlock(block)">
                    {{ block.id }}
                </div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="controls-panel">
            <!-- 细节控制 -->
            <div class="control-section">
                <h3>Detail Level Control</h3>
                <div class="detail-control">
                    <button class="btn btn-danger" @click="adjustDetail(-1)">-</button>
                    <div class="detail-value">{{ settings.detail.toFixed(2) }}</div>
                    <button class="btn btn-success" @click="adjustDetail(1)">+</button>
                </div>
                <input type="range" 
                       v-model.number="settings.detail" 
                       min="0.5" max="2.0" step="0.1"
                       @input="updateLod">
                
                <div style="margin-top: 10px; font-size: 12px; opacity: 0.8;">
                    Higher detail = better quality, lower performance
                </div>
            </div>
            
            <!-- LOD统计 -->
            <div class="control-section">
                <h3>LOD Statistics</h3>
                <div class="lod-stats">
                    <div class="lod-stat lod-0">
                        <div class="stat-value">{{ lodStats.lod0 }}</div>
                        <div class="stat-label">High Detail</div>
                    </div>
                    <div class="lod-stat lod-1">
                        <div class="stat-value">{{ lodStats.lod1 }}</div>
                        <div class="stat-label">Medium Detail</div>
                    </div>
                    <div class="lod-stat lod-2">
                        <div class="stat-value">{{ lodStats.lod2 }}</div>
                        <div class="stat-label">Low Detail</div>
                    </div>
                    <div class="lod-stat culled">
                        <div class="stat-value">{{ lodStats.culled }}</div>
                        <div class="stat-label">Culled</div>
                    </div>
                </div>
                
                <div style="margin-top: 10px;">
                    <div>Total Rendered: {{ lodStats.rendered }}</div>
                    <div>Render Ratio: {{ renderRatio.toFixed(1) }}%</div>
                </div>
            </div>
            
            <!-- 性能监控 -->
            <div class="control-section">
                <h3>Performance Monitor</h3>
                <div class="performance-meter">
                    <div class="fps-display" :class="fpsClass">{{ performance.fps }} FPS</div>
                    <div style="font-size: 12px; text-align: center; opacity: 0.7;">
                        Frame Time: {{ performance.frameTime.toFixed(1) }}ms
                    </div>
                    
                    <div style="margin-top: 10px;">
                        <div style="font-size: 12px; margin-bottom: 3px;">Memory Usage</div>
                        <div class="memory-bar">
                            <div class="memory-fill" :style="{ width: performance.memoryUsage + '%' }"></div>
                        </div>
                        <div style="font-size: 10px; opacity: 0.7; margin-top: 2px;">
                            {{ performance.memoryUsage.toFixed(1) }}% of available
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 相机控制 -->
            <div class="control-section">
                <h3>Camera Controls</h3>
                
                <label>Camera Speed: {{ settings.cameraSpeed.toFixed(1) }}</label>
                <input type="range" 
                       v-model.number="settings.cameraSpeed" 
                       min="0.1" max="3.0" step="0.1">
                
                <label>FOV: {{ settings.fov }}°</label>
                <input type="range" 
                       v-model.number="settings.fov" 
                       min="30" max="120" step="5"
                       @input="updateLod">
                
                <div style="margin-top: 10px;">
                    <button class="btn btn-primary" @click="resetCamera">Reset Camera</button>
                    <button class="btn btn-success" @click="toggleAutoMove">
                        {{ autoMove ? 'Stop' : 'Start' }} Auto Move
                    </button>
                </div>
            </div>
            
            <!-- 高级设置 -->
            <div class="control-section">
                <h3>Advanced Settings</h3>
                
                <label>
                    <input type="checkbox" v-model="settings.showLodRadii" @change="updateDisplay">
                    Show LOD Radii
                </label>
                
                <label>
                    <input type="checkbox" v-model="settings.adaptiveLod" @change="updateLod">
                    Adaptive LOD
                </label>
                
                <label>
                    <input type="checkbox" v-model="settings.frustumCulling" @change="updateLod">
                    Frustum Culling
                </label>
                
                <div style="margin-top: 10px;">
                    <button class="btn btn-warning" @click="forceReload">Force Reload All</button>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        const { createApp, ref, reactive, computed, onMounted, onUnmounted } = Vue;
        
        createApp({
            setup() {
                // 响应式数据
                const camera = reactive({ 
                    x: 50, 
                    y: 50, 
                    angle: 0,
                    position: [0, 0, 0],
                    target: [0, 0, 0],
                    focalLength: 50
                });
                
                const autoMove = ref(true);
                
                const settings = reactive({
                    detail: 1.0,
                    cameraSpeed: 1.0,
                    fov: 60,
                    showLodRadii: true,
                    adaptiveLod: true,
                    frustumCulling: false
                });
                
                const performance = reactive({
                    fps: 60,
                    frameTime: 16.67,
                    memoryUsage: 45
                });
                
                const blocks = ref([]);
                const lodStats = reactive({
                    lod0: 0,
                    lod1: 0,
                    lod2: 0,
                    culled: 0,
                    rendered: 0
                });
                
                // 计算属性
                const lodRadii = computed(() => {
                    const baseRadius = 60 * settings.detail;
                    return {
                        lod0: baseRadius,
                        lod1: baseRadius * 2,
                        lod2: baseRadius * 3
                    };
                });
                
                const renderRatio = computed(() => {
                    return blocks.value.length > 0 ? (lodStats.rendered / blocks.value.length) * 100 : 0;
                });
                
                const fpsClass = computed(() => {
                    if (performance.fps >= 50) return 'fps-good';
                    if (performance.fps >= 30) return 'fps-medium';
                    return 'fps-poor';
                });
                
                // 模拟LOD系统
                let time = 0;
                let animationId;
                
                // 初始化块
                const initializeBlocks = () => {
                    const gridSize = 10;
                    const blockSize = 25;
                    
                    blocks.value = [];
                    for (let i = 0; i < gridSize; i++) {
                        for (let j = 0; j < gridSize; j++) {
                            const id = i * gridSize + j;
                            blocks.value.push({
                                id,
                                x: (i / (gridSize - 1)) * 90 + 5,
                                y: (j / (gridSize - 1)) * 90 + 5,
                                size: blockSize,
                                distance: 0,
                                lodLevel: -1,
                                lodClass: 'culled',
                                loading: false
                            });
                        }
                    }
                };
                
                // 计算LOD
                const calculateLod = () => {
                    let stats = { lod0: 0, lod1: 0, lod2: 0, culled: 0, rendered: 0 };
                    
                    blocks.value.forEach(block => {
                        // 计算距离
                        const dx = (block.x - camera.x) * 5;
                        const dy = (block.y - camera.y) * 5;
                        block.distance = Math.sqrt(dx * dx + dy * dy);
                        
                        // 计算LOD级别
                        const baseDistance = 60 * settings.detail;
                        
                        if (block.distance < baseDistance) {
                            block.lodLevel = 0;
                            block.lodClass = 'lod-0';
                            stats.lod0++;
                            stats.rendered++;
                        } else if (block.distance < baseDistance * 2) {
                            block.lodLevel = 1;
                            block.lodClass = 'lod-1';
                            stats.lod1++;
                            stats.rendered++;
                        } else if (block.distance < baseDistance * 3) {
                            block.lodLevel = 2;
                            block.lodClass = 'lod-2';
                            stats.lod2++;
                            stats.rendered++;
                        } else {
                            block.lodLevel = -1;
                            block.lodClass = 'culled';
                            stats.culled++;
                        }
                        
                        // 视锥体剔除
                        if (settings.frustumCulling) {
                            const angle = Math.atan2(dy, dx) * 180 / Math.PI;
                            const camerAngle = camera.angle;
                            const fovHalf = settings.fov / 2;
                            
                            let angleDiff = Math.abs(angle - camerAngle);
                            if (angleDiff > 180) angleDiff = 360 - angleDiff;
                            
                            if (angleDiff > fovHalf && block.lodLevel >= 0) {
                                block.lodLevel = -1;
                                block.lodClass = 'culled';
                                stats.rendered--;
                                stats.culled++;
                                if (block.lodClass === 'lod-0') stats.lod0--;
                                else if (block.lodClass === 'lod-1') stats.lod1--;
                                else if (block.lodClass === 'lod-2') stats.lod2--;
                            }
                        }
                    });
                    
                    Object.assign(lodStats, stats);
                    
                    // 模拟性能影响
                    const renderLoad = stats.rendered / blocks.value.length;
                    performance.fps = Math.round(60 - renderLoad * 30 + Math.random() * 5);
                    performance.frameTime = 1000 / performance.fps;
                    performance.memoryUsage = 20 + renderLoad * 60 + Math.random() * 10;
                };
                
                // 更新相机
                const updateCamera = () => {
                    if (autoMove.value) {
                        time += 0.01 * settings.cameraSpeed;
                        camera.x = 50 + Math.sin(time) * 35;
                        camera.y = 50 + Math.cos(time * 0.7) * 35;
                        camera.angle = (time * 30) % 360;
                    }
                    
                    calculateLod();
                    animationId = requestAnimationFrame(updateCamera);
                };
                
                // 方法
                const adjustDetail = (delta) => {
                    const factor = delta > 0 ? Math.SQRT2 : Math.SQRT1_2;
                    settings.detail *= factor;
                    settings.detail = Math.max(0.5, Math.min(2.0, settings.detail));
                    calculateLod();
                };
                
                const updateLod = () => {
                    calculateLod();
                };
                
                const updateDisplay = () => {
                    // 更新显示设置
                };
                
                const resetCamera = () => {
                    camera.x = 50;
                    camera.y = 50;
                    camera.angle = 0;
                    calculateLod();
                };
                
                const toggleAutoMove = () => {
                    autoMove.value = !autoMove.value;
                };
                
                const forceReload = () => {
                    blocks.value.forEach(block => {
                        block.loading = true;
                        setTimeout(() => {
                            block.loading = false;
                        }, Math.random() * 2000 + 500);
                    });
                };
                
                const selectBlock = (block) => {
                    console.log('Selected block:', block);
                };
                
                // 生命周期
                onMounted(() => {
                    initializeBlocks();
                    calculateLod();
                    animationId = requestAnimationFrame(updateCamera);
                });
                
                onUnmounted(() => {
                    if (animationId) {
                        cancelAnimationFrame(animationId);
                    }
                });
                
                return {
                    camera,
                    autoMove,
                    settings,
                    performance,
                    blocks,
                    lodStats,
                    lodRadii,
                    renderRatio,
                    fpsClass,
                    adjustDetail,
                    updateLod,
                    updateDisplay,
                    resetCamera,
                    toggleAutoMove,
                    forceReload,
                    selectBlock
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
