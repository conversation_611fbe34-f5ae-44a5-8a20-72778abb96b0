<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Splatter.app - Simple Loading Strategy Demo</title>
    
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #fff;
            overflow: hidden;
        }
        
        .demo-container {
            display: flex;
            height: 100vh;
        }
        
        .viewport {
            flex: 1;
            position: relative;
            background: linear-gradient(45deg, #2a2a2a, #1a1a1a);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .scene-visualization {
            width: 400px;
            height: 400px;
            border: 2px solid #333;
            position: relative;
            background: radial-gradient(circle, #333 1px, transparent 1px);
            background-size: 20px 20px;
        }
        
        .block {
            position: absolute;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: bold;
        }
        
        .block.loaded { background: rgba(76, 175, 80, 0.8); color: white; }
        .block.loading { background: rgba(255, 193, 7, 0.8); color: black; }
        .block.queued { background: rgba(33, 150, 243, 0.6); color: white; }
        .block.unloaded { background: rgba(128, 128, 128, 0.3); color: #ccc; }
        .block.distant { background: rgba(244, 67, 54, 0.4); color: #ccc; }
        
        .camera {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #FF5722;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            box-shadow: 0 0 10px rgba(255, 87, 34, 0.8);
        }
        
        .load-radius {
            position: absolute;
            border: 2px dashed rgba(76, 175, 80, 0.5);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
        }
        
        .controls-panel {
            width: 300px;
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            overflow-y: auto;
        }
        
        .control-section {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        
        .control-section h3 {
            margin-bottom: 10px;
            color: #4CAF50;
            font-size: 14px;
        }
        
        .stat-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .stat-label {
            font-size: 10px;
            opacity: 0.7;
        }
        
        .loading-queue {
            max-height: 150px;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            padding: 8px;
        }
        
        .queue-item {
            display: flex;
            justify-content: space-between;
            padding: 4px 8px;
            margin: 2px 0;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .queue-item.loading { background: rgba(255, 193, 7, 0.3); }
        .queue-item.queued { background: rgba(33, 150, 243, 0.3); }
        
        .btn {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin: 2px;
            transition: all 0.2s;
        }
        
        .btn-primary { background: #2196F3; color: white; }
        .btn-success { background: #4CAF50; color: white; }
        .btn-warning { background: #FF9800; color: white; }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div id="app" class="demo-container">
        <!-- 主视口 -->
        <div class="viewport">
            <div class="scene-visualization">
                <!-- 加载半径指示器 -->
                <div class="load-radius" 
                     :style="{
                         left: camera.x + '%',
                         top: camera.y + '%',
                         width: loadRadius * 2 + 'px',
                         height: loadRadius * 2 + 'px'
                     }"></div>
                
                <!-- 相机 -->
                <div class="camera" 
                     :style="{ left: camera.x + '%', top: camera.y + '%' }"></div>
                
                <!-- 数据块 -->
                <div v-for="block in blocks" 
                     :key="block.id"
                     class="block"
                     :class="block.status"
                     :style="{
                         left: block.x + '%',
                         top: block.y + '%',
                         width: block.size + 'px',
                         height: block.size + 'px'
                     }"
                     :title="`Block ${block.id} - Distance: ${block.distance.toFixed(1)}`">
                    {{ block.id }}
                </div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="controls-panel">
            <!-- 加载统计 -->
            <div class="control-section">
                <h3>Loading Statistics</h3>
                <div class="stat-grid">
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.totalBlocks }}</div>
                        <div class="stat-label">Total Blocks</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.loadedBlocks }}</div>
                        <div class="stat-label">Loaded</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.loadingBlocks }}</div>
                        <div class="stat-label">Loading</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ stats.queuedBlocks }}</div>
                        <div class="stat-label">Queued</div>
                    </div>
                </div>
                
                <div style="margin-top: 10px;">
                    <div>Load Progress: {{ loadProgress.toFixed(1) }}%</div>
                    <div>Concurrent Loads: {{ settings.maxConcurrentLoads }}</div>
                    <div>Load Radius: {{ settings.loadRadius.toFixed(0) }}px</div>
                </div>
            </div>
            
            <!-- 设置控制 -->
            <div class="control-section">
                <h3>Settings</h3>
                
                <label>Max Concurrent Loads: {{ settings.maxConcurrentLoads }}</label>
                <input type="range" 
                       v-model.number="settings.maxConcurrentLoads" 
                       min="1" max="12" step="1"
                       @input="updateSettings">
                
                <label>Load Radius: {{ settings.loadRadius.toFixed(0) }}</label>
                <input type="range" 
                       v-model.number="settings.loadRadius" 
                       min="50" max="200" step="5"
                       @input="updateSettings">
                
                <label>Camera Speed: {{ settings.cameraSpeed.toFixed(1) }}</label>
                <input type="range" 
                       v-model.number="settings.cameraSpeed" 
                       min="0.1" max="2.0" step="0.1">
            </div>
            
            <!-- 加载队列 -->
            <div class="control-section">
                <h3>Loading Queue</h3>
                <div class="loading-queue">
                    <div v-for="item in loadingQueue" 
                         :key="item.id"
                         class="queue-item"
                         :class="item.status">
                        <span>Block {{ item.id }}</span>
                        <span>{{ item.distance.toFixed(1) }}</span>
                    </div>
                </div>
            </div>
            
            <!-- 控制按钮 -->
            <div class="control-section">
                <h3>Controls</h3>
                <button class="btn btn-primary" @click="resetCamera">Reset Camera</button>
                <button class="btn btn-success" @click="toggleAutoMove">
                    {{ autoMove ? 'Stop' : 'Start' }} Auto Move
                </button>
                <button class="btn btn-warning" @click="clearAllBlocks">Clear All</button>
                
                <div style="margin-top: 10px;">
                    <label>
                        <input type="checkbox" v-model="showDistances">
                        Show Distances
                    </label>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        const { createApp, ref, reactive, computed, onMounted, onUnmounted } = Vue;
        
        createApp({
            setup() {
                // 响应式数据
                const camera = reactive({ x: 50, y: 50 });
                const autoMove = ref(true);
                const showDistances = ref(false);
                
                const settings = reactive({
                    maxConcurrentLoads: 6,
                    loadRadius: 80,
                    cameraSpeed: 1.0
                });
                
                const stats = reactive({
                    totalBlocks: 64,
                    loadedBlocks: 0,
                    loadingBlocks: 0,
                    queuedBlocks: 0
                });
                
                const blocks = ref([]);
                const loadingQueue = ref([]);
                
                // 模拟数据加载器状态
                const loader = reactive({
                    loadingSet: new Set(),
                    loadedSet: new Set(),
                    loadQueue: []
                });
                
                // 计算属性
                const loadProgress = computed(() => {
                    return stats.totalBlocks > 0 ? (stats.loadedBlocks / stats.totalBlocks) * 100 : 0;
                });
                
                // 初始化块
                const initializeBlocks = () => {
                    const gridSize = 8; // 8x8 grid
                    const blockSize = 30;
                    const spacing = 50;
                    
                    blocks.value = [];
                    for (let i = 0; i < gridSize; i++) {
                        for (let j = 0; j < gridSize; j++) {
                            const id = i * gridSize + j;
                            blocks.value.push({
                                id,
                                x: (i / (gridSize - 1)) * 80 + 10, // 10% to 90%
                                y: (j / (gridSize - 1)) * 80 + 10,
                                size: blockSize,
                                status: 'unloaded',
                                distance: 0
                            });
                        }
                    }
                    
                    stats.totalBlocks = blocks.value.length;
                };
                
                // 计算距离
                const calculateDistance = (block) => {
                    const dx = (block.x - camera.x) * 4; // 缩放因子
                    const dy = (block.y - camera.y) * 4;
                    return Math.sqrt(dx * dx + dy * dy);
                };
                
                // 更新块状态
                const updateBlockStates = () => {
                    // 计算所有块的距离
                    blocks.value.forEach(block => {
                        block.distance = calculateDistance(block);
                    });
                    
                    // 确定可见块
                    const visibleBlocks = blocks.value
                        .filter(block => block.distance <= settings.loadRadius)
                        .sort((a, b) => a.distance - b.distance);
                    
                    // 更新加载队列
                    const newQueue = visibleBlocks
                        .filter(block => !loader.loadedSet.has(block.id) && !loader.loadingSet.has(block.id))
                        .slice(0, 20); // 限制队列长度
                    
                    loader.loadQueue = newQueue.map(block => block.id);
                    
                    // 处理加载队列
                    processLoadQueue();
                    
                    // 更新块状态显示
                    blocks.value.forEach(block => {
                        if (loader.loadedSet.has(block.id)) {
                            block.status = 'loaded';
                        } else if (loader.loadingSet.has(block.id)) {
                            block.status = 'loading';
                        } else if (loader.loadQueue.includes(block.id)) {
                            block.status = 'queued';
                        } else if (block.distance > settings.loadRadius) {
                            block.status = 'distant';
                        } else {
                            block.status = 'unloaded';
                        }
                    });
                    
                    // 更新统计
                    stats.loadedBlocks = loader.loadedSet.size;
                    stats.loadingBlocks = loader.loadingSet.size;
                    stats.queuedBlocks = loader.loadQueue.length;
                    
                    // 更新加载队列显示
                    updateLoadingQueueDisplay();
                };
                
                // 处理加载队列
                const processLoadQueue = () => {
                    for (const blockId of loader.loadQueue) {
                        if (loader.loadingSet.size >= settings.maxConcurrentLoads) break;
                        
                        if (!loader.loadedSet.has(blockId) && !loader.loadingSet.has(blockId)) {
                            startBlockLoad(blockId);
                        }
                    }
                };
                
                // 开始加载块
                const startBlockLoad = (blockId) => {
                    loader.loadingSet.add(blockId);
                    
                    // 模拟加载时间
                    const loadTime = 500 + Math.random() * 1000;
                    
                    setTimeout(() => {
                        loader.loadingSet.delete(blockId);
                        loader.loadedSet.add(blockId);
                        
                        // 继续处理队列
                        processLoadQueue();
                    }, loadTime);
                };
                
                // 更新加载队列显示
                const updateLoadingQueueDisplay = () => {
                    const queueItems = [];
                    
                    // 正在加载的块
                    loader.loadingSet.forEach(blockId => {
                        const block = blocks.value.find(b => b.id === blockId);
                        if (block) {
                            queueItems.push({
                                id: blockId,
                                distance: block.distance,
                                status: 'loading'
                            });
                        }
                    });
                    
                    // 队列中的块
                    loader.loadQueue.forEach(blockId => {
                        if (!loader.loadingSet.has(blockId)) {
                            const block = blocks.value.find(b => b.id === blockId);
                            if (block) {
                                queueItems.push({
                                    id: blockId,
                                    distance: block.distance,
                                    status: 'queued'
                                });
                            }
                        }
                    });
                    
                    // 按距离排序
                    queueItems.sort((a, b) => a.distance - b.distance);
                    loadingQueue.value = queueItems.slice(0, 10); // 只显示前10个
                };
                
                // 相机自动移动
                let animationId;
                let time = 0;
                
                const updateCamera = () => {
                    if (autoMove.value) {
                        time += 0.01 * settings.cameraSpeed;
                        camera.x = 50 + Math.sin(time) * 30;
                        camera.y = 50 + Math.cos(time * 0.7) * 30;
                    }
                    
                    updateBlockStates();
                    animationId = requestAnimationFrame(updateCamera);
                };
                
                // 方法
                const updateSettings = () => {
                    updateBlockStates();
                };
                
                const resetCamera = () => {
                    camera.x = 50;
                    camera.y = 50;
                    updateBlockStates();
                };
                
                const toggleAutoMove = () => {
                    autoMove.value = !autoMove.value;
                };
                
                const clearAllBlocks = () => {
                    loader.loadedSet.clear();
                    loader.loadingSet.clear();
                    loader.loadQueue = [];
                    updateBlockStates();
                };
                
                // 生命周期
                onMounted(() => {
                    initializeBlocks();
                    updateBlockStates();
                    animationId = requestAnimationFrame(updateCamera);
                });
                
                onUnmounted(() => {
                    if (animationId) {
                        cancelAnimationFrame(animationId);
                    }
                });
                
                return {
                    camera,
                    autoMove,
                    showDistances,
                    settings,
                    stats,
                    blocks,
                    loadingQueue,
                    loadProgress,
                    loadRadius: computed(() => settings.loadRadius),
                    updateSettings,
                    resetCamera,
                    toggleAutoMove,
                    clearAllBlocks
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
