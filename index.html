<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Splatter.app Viewer - 3D Gaussian Splatting Viewer</title>
    
    <!-- Vue.js 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: #fff;
            overflow: hidden;
        }
        
        canvas {
            display: block;
            width: 100vw;
            height: 100vh;
            touch-action: none;
        }
        
        .viewer-ui {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            pointer-events: none;
            width: 100%;
            height: 100%;
        }
        
        .control-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            min-width: 200px;
            pointer-events: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group:last-child {
            margin-bottom: 0;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            text-transform: uppercase;
            opacity: 0.8;
            font-weight: 500;
        }
        
        .control-group select,
        .control-group input[type="color"] {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }
        
        .control-group input[type="color"] {
            height: 40px;
            cursor: pointer;
        }
        
        .up-direction-controls {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 5px;
        }
        
        .up-direction-controls button {
            padding: 8px;
            border: none;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s;
        }
        
        .up-direction-controls button:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .up-direction-controls button.active {
            background: #007bff;
        }
        
        .custom-up-info {
            margin-top: 5px;
            font-size: 11px;
            opacity: 0.7;
            font-family: monospace;
        }
        
        .btn {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
            margin-bottom: 5px;
            transition: all 0.2s;
            font-weight: 500;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .btn-primary { 
            background: #007bff; 
            color: white; 
        }
        
        .btn-secondary { 
            background: #6c757d; 
            color: white; 
        }
        
        .btn-success { 
            background: #28a745; 
            color: white; 
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 20px;
            border-radius: 6px;
            opacity: 0;
            transition: opacity 0.3s;
            pointer-events: auto;
            font-size: 14px;
            font-weight: 500;
            max-width: 400px;
            text-align: center;
        }
        
        .alert.show {
            opacity: 1;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px 30px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 15px;
            pointer-events: auto;
            backdrop-filter: blur(10px);
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff33;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .info-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-panel div {
            margin-bottom: 3px;
        }
        
        .info-panel div:last-child {
            margin-bottom: 0;
        }
        
        @media (max-width: 768px) {
            .control-panel {
                top: 10px;
                right: 10px;
                left: 10px;
                min-width: auto;
            }
            
            .info-panel {
                bottom: 10px;
                left: 10px;
                right: 10px;
            }
            
            .up-direction-controls {
                grid-template-columns: repeat(4, 1fr);
            }
        }
        
        /* 配置模式背景 */
        body.config-mode {
            background-image: 
                linear-gradient(to right, rgba(192, 192, 192, 0.75), rgba(192, 192, 192, 0.75)),
                linear-gradient(to right, black 50%, white 50%),
                linear-gradient(to bottom, black 50%, white 50%);
            background-blend-mode: normal, difference, normal;
            background-size: 2em 2em;
        }
    </style>
</head>
<body>
    <!-- Vue.js UI -->
    <div id="viewer-ui" class="viewer-ui">
        <!-- 主控制面板 -->
        <div class="control-panel" :class="{ 'config-mode': configMode }">
            <!-- 分辨率控制 -->
            <div class="control-group">
                <label>Resolution:</label>
                <select v-model="resolution">
                    <option value="low">Low (1x)</option>
                    <option value="high">High (2x)</option>
                </select>
            </div>
            
            <!-- 上方向控制 -->
            <div class="control-group" v-if="!isMobile">
                <label>Up Direction:</label>
                <div class="up-direction-controls">
                    <button @click="setUpDirection('up')" 
                            :class="{ active: !customUp && Math.abs(up[1] - 1) < 0.01 }">
                        ↑ Y+
                    </button>
                    <button @click="setUpDirection('down')" 
                            :class="{ active: !customUp && Math.abs(up[1] + 1) < 0.01 }">
                        ↓ Y-
                    </button>
                    <button @click="setUpDirection('forward')" 
                            :class="{ active: !customUp && Math.abs(up[2] - 1) < 0.01 }">
                        → Z+
                    </button>
                    <button @click="setUpDirection('back')" 
                            :class="{ active: !customUp && Math.abs(up[2] + 1) < 0.01 }">
                        ← Z-
                    </button>
                </div>
                <div v-if="customUp" class="custom-up-info">
                    Custom: ({{ up[0].toFixed(2) }}, {{ up[1].toFixed(2) }}, {{ up[2].toFixed(2) }})
                </div>
            </div>
            
            <!-- 背景颜色控制 -->
            <div class="control-group" v-if="configMode">
                <label>Background Color:</label>
                <input type="color" 
                       :value="rgbToHex(backgroundColor)" 
                       @input="setBackgroundColor($event.target.value)">
            </div>
            
            <!-- 操作按钮 -->
            <div class="control-group">
                <button @click="resetView()" class="btn btn-secondary">Reset View</button>
                <button @click="toggleAutoRotate()" class="btn btn-secondary">
                    {{ autoRotating ? 'Stop' : 'Auto' }} Rotate
                </button>
                <button @click="copyViewParams()" class="btn btn-secondary">Copy View</button>
            </div>
            
            <!-- 全屏按钮 -->
            <div class="control-group" v-if="isFullscreenEnabled()">
                <button @click="toggleFullscreen()" class="btn btn-primary">
                    {{ isFullscreen ? 'Exit Fullscreen' : 'Fullscreen' }}
                </button>
            </div>
            
            <!-- 保存配置按钮 -->
            <div class="control-group" v-if="configMode">
                <button @click="saveConfiguration()" 
                        class="btn btn-success" 
                        :disabled="saving">
                    {{ saving ? 'Saving...' : 'Save Configuration' }}
                </button>
            </div>
        </div>
        
        <!-- 信息面板 -->
        <div class="info-panel" v-if="showInfo">
            <div>FPS: {{ fps }}</div>
            <div>Splats: {{ formatNumber(splatCount) }}</div>
            <div>Loaded: {{ loadProgress }}%</div>
            <div v-if="isMobile">Touch: Rotate | Pinch: Zoom</div>
            <div v-else>Mouse: Rotate | Wheel: Zoom | Right: Pan</div>
        </div>
        
        <!-- 提示信息 -->
        <div class="alert" :class="alertClass" v-if="alertMessage">
            {{ alertMessage }}
        </div>
        
        <!-- 加载指示器 -->
        <div class="loading-indicator" v-if="saving">
            <div class="spinner"></div>
            <span>Saving configuration...</span>
        </div>
    </div>

    <!-- 应用脚本 -->
    <script type="module">
        // 模拟的应用配置
        window.config = {
            splatId: null,
            defaultView: [0, 0, 0, 0, 0, 1],
            upDirection: [0, 1, 0],
            backgroundColor: [0, 0, 0, 1]
        };
        
        // 检查是否为移动设备
        function isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }
        
        // RGB转十六进制
        function rgbToHex(rgb) {
            if (Array.isArray(rgb)) {
                const [r, g, b] = rgb;
                const toHex = (c) => {
                    const hex = Math.round(c * 255).toString(16);
                    return hex.length === 1 ? '0' + hex : hex;
                };
                return '#' + toHex(r) + toHex(g) + toHex(b);
            }
            return '#000000';
        }
        
        // 十六进制转RGB
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? [
                parseInt(result[1], 16) / 255,
                parseInt(result[2], 16) / 255,
                parseInt(result[3], 16) / 255,
                1
            ] : [0, 0, 0, 1];
        }
        
        // 格式化数字
        function formatNumber(num) {
            return num.toLocaleString();
        }
        
        // 初始化Vue应用
        const { createApp, reactive, ref, computed } = Vue;
        
        createApp({
            setup() {
                // 响应式数据
                const configMode = new URLSearchParams(window.location.search).has('configure');
                const resolution = ref('low');
                const up = reactive([0, 1, 0]);
                const backgroundColor = reactive([0, 0, 0, 1]);
                const saving = ref(false);
                const isFullscreen = ref(false);
                const alertMessage = ref('');
                const alertClass = ref('');
                const autoRotating = ref(false);
                const showInfo = ref(true);
                const fps = ref(60);
                const splatCount = ref(1000000);
                const loadProgress = ref(100);
                
                // 计算属性
                const customUp = computed(() => {
                    const threshold = 0.01;
                    const isStandardUp = Math.abs(up[1] - 1) < threshold && Math.abs(up[0]) < threshold && Math.abs(up[2]) < threshold;
                    const isStandardDown = Math.abs(up[1] + 1) < threshold && Math.abs(up[0]) < threshold && Math.abs(up[2]) < threshold;
                    const isStandardForward = Math.abs(up[2] - 1) < threshold && Math.abs(up[0]) < threshold && Math.abs(up[1]) < threshold;
                    const isStandardBack = Math.abs(up[2] + 1) < threshold && Math.abs(up[0]) < threshold && Math.abs(up[1]) < threshold;
                    
                    return !(isStandardUp || isStandardDown || isStandardForward || isStandardBack);
                });
                
                // 方法
                const setUpDirection = (direction) => {
                    const directions = {
                        'up': [0, 1, 0],
                        'down': [0, -1, 0],
                        'forward': [0, 0, 1],
                        'back': [0, 0, -1]
                    };
                    
                    if (directions[direction]) {
                        up[0] = directions[direction][0];
                        up[1] = directions[direction][1];
                        up[2] = directions[direction][2];
                    }
                };
                
                const setBackgroundColor = (hex) => {
                    const rgb = hexToRgb(hex);
                    backgroundColor[0] = rgb[0];
                    backgroundColor[1] = rgb[1];
                    backgroundColor[2] = rgb[2];
                    backgroundColor[3] = rgb[3];
                };
                
                const resetView = () => {
                    showSuccessAlert('View reset to default');
                };
                
                const toggleAutoRotate = () => {
                    autoRotating.value = !autoRotating.value;
                    showSuccessAlert(autoRotating.value ? 'Auto rotation enabled' : 'Auto rotation disabled');
                };
                
                const copyViewParams = () => {
                    const viewParams = {
                        pose: [0, 0, 0, 0, 0, 1]
                    };
                    navigator.clipboard.writeText(JSON.stringify(viewParams));
                    showSuccessAlert('View parameters copied to clipboard');
                };
                
                const isFullscreenEnabled = () => {
                    return !!document.fullscreenEnabled;
                };
                
                const toggleFullscreen = () => {
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    } else {
                        document.body.requestFullscreen();
                    }
                };
                
                const saveConfiguration = async () => {
                    saving.value = true;
                    
                    try {
                        // 模拟保存过程
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        showSuccessAlert('Configuration saved successfully');
                    } catch (error) {
                        showErrorAlert(`Save failed: ${error.message}`);
                    } finally {
                        saving.value = false;
                    }
                };
                
                const showSuccessAlert = (message) => {
                    alertMessage.value = message;
                    alertClass.value = 'alert-success show';
                    
                    setTimeout(() => {
                        alertClass.value = 'alert-success';
                    }, 3000);
                };
                
                const showErrorAlert = (message) => {
                    alertMessage.value = message;
                    alertClass.value = 'alert-danger show';
                    
                    setTimeout(() => {
                        alertClass.value = 'alert-danger';
                    }, 5000);
                };
                
                // 监听全屏状态变化
                document.addEventListener('fullscreenchange', () => {
                    isFullscreen.value = !!document.fullscreenElement;
                });
                
                // 设置配置模式背景
                if (configMode) {
                    document.body.classList.add('config-mode');
                }
                
                return {
                    configMode,
                    resolution,
                    up,
                    backgroundColor,
                    saving,
                    isFullscreen,
                    alertMessage,
                    alertClass,
                    autoRotating,
                    showInfo,
                    fps,
                    splatCount,
                    loadProgress,
                    customUp,
                    isMobile: isMobile(),
                    setUpDirection,
                    setBackgroundColor,
                    resetView,
                    toggleAutoRotate,
                    copyViewParams,
                    isFullscreenEnabled,
                    toggleFullscreen,
                    saveConfiguration,
                    rgbToHex,
                    formatNumber
                };
            }
        }).mount('#viewer-ui');
        
        // 创建画布用于演示
        const canvas = document.createElement('canvas');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        document.body.appendChild(canvas);
        
        // 简单的WebGL演示
        const gl = canvas.getContext('webgl2');
        if (gl) {
            gl.clearColor(0, 0, 0, 1);
            
            function render() {
                gl.clear(gl.COLOR_BUFFER_BIT);
                requestAnimationFrame(render);
            }
            
            render();
        }
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            if (gl) {
                gl.viewport(0, 0, canvas.width, canvas.height);
            }
        });
        
        console.log('Splatter.app Viewer Demo - Architecture restored successfully!');
        console.log('This is a demonstration of the reconstructed architecture.');
        console.log('For full functionality, implement the actual 3D Gaussian Splatting renderer.');
    </script>
</body>
</html>
