/**
 * Splatter.app Viewer - Worker Manager (简化版)
 * 管理Web Workers进行数据处理
 */

export class WorkerManager {
    constructor(blockSize, workerCount = 3) {
        this.blockSize = blockSize;
        this.workers = [];
        this.taskQueue = [];
        this.busyWorkers = new Set();
        this.nextTaskId = 0;
        this.pendingTasks = new Map();

        // 创建工作线程
        for (let i = 0; i < workerCount; i++) {
            const worker = this.createWorker();
            this.workers.push(worker);
        }
    }

    createWorker() {
        const workerCode = this.generateWorkerCode();
        const blob = new Blob([workerCode], { type: 'text/javascript' });
        const url = URL.createObjectURL(blob);
        const worker = new Worker(url);

        worker.onmessage = (e) => {
            const { taskId, result, error } = e.data;
            const task = this.pendingTasks.get(taskId);
            if (task) {
                this.pendingTasks.delete(taskId);
                this.busyWorkers.delete(worker);

                if (error) {
                    task.reject(new Error(error));
                } else {
                    task.resolve(result);
                }

                this.processNextTask();
            }
        };

        URL.revokeObjectURL(url);
        return worker;
    }

    generateWorkerCode() {
        return `
            self.onmessage = function(e) {
                const { taskId, data, type } = e.data;

                try {
                    let result;

                    if (type === 'processBlock') {
                        result = processBlock(data);
                    } else {
                        throw new Error('Unknown task type: ' + type);
                    }

                    self.postMessage({ taskId, result });
                } catch (error) {
                    self.postMessage({ taskId, error: error.message });
                }
            };

            function processBlock(arrayBuffer) {
                const view = new DataView(arrayBuffer);
                let offset = 0;

                const splatCount = view.getUint32(offset, true);
                offset += 4;

                const positions = new Float32Array(splatCount * 3);
                const colors = new Uint8Array(splatCount * 4);
                const scales = new Float32Array(splatCount * 3);
                const rotations = new Float32Array(splatCount * 4);

                for (let i = 0; i < splatCount; i++) {
                    positions[i * 3] = view.getFloat32(offset, true);
                    positions[i * 3 + 1] = view.getFloat32(offset + 4, true);
                    positions[i * 3 + 2] = view.getFloat32(offset + 8, true);
                    offset += 12;

                    colors[i * 4] = view.getUint8(offset);
                    colors[i * 4 + 1] = view.getUint8(offset + 1);
                    colors[i * 4 + 2] = view.getUint8(offset + 2);
                    colors[i * 4 + 3] = view.getUint8(offset + 3);
                    offset += 4;

                    scales[i * 3] = view.getFloat32(offset, true);
                    scales[i * 3 + 1] = view.getFloat32(offset + 4, true);
                    scales[i * 3 + 2] = view.getFloat32(offset + 8, true);
                    offset += 12;

                    rotations[i * 4] = view.getFloat32(offset, true);
                    rotations[i * 4 + 1] = view.getFloat32(offset + 4, true);
                    rotations[i * 4 + 2] = view.getFloat32(offset + 8, true);
                    rotations[i * 4 + 3] = view.getFloat32(offset + 12, true);
                    offset += 16;
                }

                return {
                    splatCount,
                    positions,
                    colors,
                    scales,
                    rotations
                };
            }
        `;
    }

    async processBlock(data) {
        return new Promise((resolve, reject) => {
            const taskId = this.nextTaskId++;
            this.pendingTasks.set(taskId, { resolve, reject });

            const availableWorker = this.getAvailableWorker();
            if (availableWorker) {
                this.busyWorkers.add(availableWorker);
                availableWorker.postMessage({
                    taskId,
                    data,
                    type: 'processBlock'
                });
            } else {
                this.taskQueue.push({ taskId, data, type: 'processBlock' });
            }
        });
    }

    getAvailableWorker() {
        return this.workers.find(worker => !this.busyWorkers.has(worker));
    }

    processNextTask() {
        if (this.taskQueue.length === 0) return;

        const availableWorker = this.getAvailableWorker();
        if (availableWorker) {
            const task = this.taskQueue.shift();
            this.busyWorkers.add(availableWorker);
            availableWorker.postMessage(task);
        }
    }

    terminate() {
        this.workers.forEach(worker => worker.terminate());
        this.workers = [];
        this.busyWorkers.clear();
        this.taskQueue = [];
        this.pendingTasks.clear();
    }
}