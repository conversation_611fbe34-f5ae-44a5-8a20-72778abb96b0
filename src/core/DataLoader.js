/**
 * Splatter.app Viewer - Data Loader (基于原始代码逻辑)
 * 负责加载和管理3D散点数据，基于原始代码的简单分块机制
 */

import { WorkerManager } from './WorkerManager.js';

const DATA_BASE_URL = 'https://data.splatter.app';

export class DataLoader {
    constructor(config) {
        // 基础配置
        this.splatId = config.splatId ?? null;
        this.defaultView = config.defaultView ?? [0, 0, 0, 0, 0, 1];
        this.upDirection = config.upDirection ?? null;
        this.backgroundColor = config.backgroundColor ?? [0, 0, 0, 1];

        // 数据源URL
        this.baseUrl = '/';
        if (this.splatId) {
            this.baseUrl = `${DATA_BASE_URL}/${this.splatId}`;
        } else {
            const urlParams = new URLSearchParams(document.location.search);
            if (urlParams.has('id')) {
                this.splatId = urlParams.get('id');
                this.baseUrl = `${DATA_BASE_URL}/${this.splatId}`;
            } else if (urlParams.has('src')) {
                this.baseUrl = `/${urlParams.get('src')}`;
            }
        }

        // 元数据
        this.size = 0;
        this.ratio = 1.0;
        this.root = { size: 0, radius: 1 };
        this.blockSize = 4096;
        this.blockCount = 0;
        this.colorMap = [255, 255, 255, 255];
        this.filter2d = null;

        // 加载管理
        this.loadQueue = [];
        this.loadingSet = new Set();
        this.maxConcurrentLoads = 6;
        this.onBlockLoaded = () => {};
        this.loadedBlocks = [];
        this.loadedCount = 0;

        // 工作线程管理
        this.workerManager = null;

        // 初始化
        this.ready = this.loadMetadata(`${this.baseUrl}/meta`).then(metadata => {
            this.processMetadata(metadata);
        }).then(() => {
            this.workerManager = new WorkerManager(this.blockSize, 3);
        });
    }

    async loadMetadata(url) {
        if ('meta' in globalThis) {
            return Promise.resolve(globalThis.meta);
        }

        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Error fetching ${url}: ${response.status} ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Metadata loading failed:', error);
            throw error;
        }
    }

    processMetadata(metadata) {
        this.size = metadata.size;
        this.ratio = metadata.ratio;
        this.root = {
            size: metadata.root.size,
            radius: metadata.root.radius
        };
        this.blockSize = metadata.block;
        this.colorMap = metadata.colorMap ?? this.colorMap;
        this.filter2d = metadata.filter2d;
        this.upDirection = this.upDirection ?? metadata.up ?? [0, 0, 1];

        while (this.colorMap.length % 4 !== 0) {
            this.colorMap.push(0);
        }

        this.blockCount = Math.ceil(this.size / this.blockSize);
        this.loadedBlocks = new Uint8Array(this.blockCount);

        console.log('DataLoader initialized:', {
            size: this.size,
            blockCount: this.blockCount,
            blockSize: this.blockSize,
            sceneRadius: this.root.radius
        });
    }

    calculateDistance(cameraPos, blockCenter) {
        const dx = cameraPos[0] - blockCenter[0];
        const dy = cameraPos[1] - blockCenter[1];
        const dz = cameraPos[2] - blockCenter[2];
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    async loadBinaryData(url) {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Error fetching ${url}: ${response.status} ${response.statusText}`);
        }
        return response.arrayBuffer();
    }

    async loadBlock(blockId) {
        const url = `${this.baseUrl}/${blockId}`;

        try {
            const data = await this.loadBinaryData(url);

            if (this.workerManager) {
                return await this.workerManager.processBlock(data);
            }

            return data;
        } catch (error) {
            console.error(`Block ${blockId} load failed:`, error);
            throw error;
        }
    }

    processLoadQueue() {
        while (this.loadingSet.size < this.maxConcurrentLoads && this.loadQueue.length > 0) {
            const blockId = this.loadQueue.shift();

            if (this.loadedBlocks[blockId] || this.loadingSet.has(blockId)) {
                continue;
            }

            this.loadingSet.add(blockId);

            this.loadBlock(blockId).then(data => {
                this.loadedBlocks[blockId] = 1;
                this.loadedCount++;
                this.loadingSet.delete(blockId);

                this.onBlockLoaded(blockId, data);
                this.processLoadQueue();
            }).catch(error => {
                console.error(`Failed to load block ${blockId}:`, error);
                this.loadingSet.delete(blockId);
                this.processLoadQueue();
            });
        }
    }

    /**
     * 请求加载块（按优先级排序）
     */
    requestBlocks(blockIds) {
        const newBlocks = blockIds.filter(id =>
            !this.loadedBlocks[id] && !this.loadingSet.has(id)
        );

        // 将新块添加到队列前面（高优先级）
        this.loadQueue = [...newBlocks, ...this.loadQueue.filter(id => !newBlocks.includes(id))];
        this.processLoadQueue();
    }

    getLoadProgress() {
        return {
            loaded: this.loadedCount,
            total: this.blockCount,
            percentage: this.blockCount > 0 ? (this.loadedCount / this.blockCount) * 100 : 0
        };
    }

    cleanup() {
        if (this.workerManager) {
            this.workerManager.terminate();
        }
        this.loadQueue = [];
        this.loadingSet.clear();
    }
}