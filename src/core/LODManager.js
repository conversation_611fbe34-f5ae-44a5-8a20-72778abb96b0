/**
 * Splatter.app Viewer - LOD Manager (基于原始代码的receiveLod机制)
 * 负责管理细节级别，严格按照splatter-minified.txt的逻辑实现
 */

export class LODManager {
    constructor(dataLoader) {
        this.dataLoader = dataLoader;
        this.currentCamera = null;
        this.detail = 1.0;  // 细节参数，对应原始代码的detail

        // 基于原始代码的简单状态管理
        this.visibleBlocks = new Set();
        this.lastEye = [0, 0, 0];      // 上次相机位置
        this.lastLook = [0, 0, -1];    // 上次相机朝向
        this.lastDetail = this.detail;  // 上次细节级别

        // 更新控制（对应原始代码的yA标志）
        this.needsUpdate = false;
        this.lastUpdateTime = 0;
        this.updateThreshold = 100; // 100ms更新间隔
    }

    /**
     * 设置相机参数
     */
    setCamera(camera) {
        this.currentCamera = camera;
        this.requestUpdate();
    }

    /**
     * 设置细节级别 (对应原始代码的SA方法)
     */
    setDetail(detail) {
        this.detail = Math.max(0.5, detail); // 原始代码中最小值为0.5
        this.requestUpdate();
    }

    /**
     * 调整细节级别 (对应原始代码的SA(A)方法)
     */
    adjustDetail(direction) {
        // 原始代码逻辑：this.detail *= A > 0 ? Math.SQRT1_2 : Math.SQRT2
        this.detail *= direction > 0 ? Math.SQRT1_2 : Math.SQRT2;
        this.detail = Math.max(this.detail, 0.5);
        this.requestUpdate();
        console.log(`Detail: ${this.detail}`);
    }

    /**
     * 请求更新LOD（对应原始代码的eA方法逻辑）
     */
    requestUpdate() {
        if (!this.currentCamera || this.dataLoader.blockCount === 0) {
            return;
        }

        // 检查是否需要更新（对应原始代码的变化检测）
        if (!this.needsUpdate && !this.hasCameraChanged(this.currentCamera)) {
            return;
        }

        // 节流控制
        const now = Date.now();
        if (now - this.lastUpdateTime < this.updateThreshold) {
            return;
        }
        this.lastUpdateTime = now;

        // 标记需要更新
        this.needsUpdate = true;
        this.updateLOD();
    }

    /**
     * 更新LOD - 基于原始代码的简单距离判断
     */
    updateLOD() {
        const camera = this.currentCamera;
        const cameraPos = [camera.position.x, camera.position.y, camera.position.z];
        const sceneRadius = this.dataLoader.root.radius;

        // 基于原始代码的简单距离计算
        const baseDistance = sceneRadius * this.detail;
        const maxDistance = baseDistance * 2; // 简单的2倍距离阈值

        const newVisibleBlocks = new Set();
        const blockPriorities = []; // 用于距离排序

        // 简单的块可见性判断（对应原始代码逻辑）
        for (let blockId = 0; blockId < this.dataLoader.blockCount; blockId++) {
            const blockCenter = this.estimateBlockCenter(blockId, sceneRadius);
            const distance = this.dataLoader.calculateDistance(cameraPos, blockCenter);

            // 基于距离和细节参数判断是否需要加载
            if (distance < maxDistance) {
                newVisibleBlocks.add(blockId);

                // 计算简单的优先级（距离越近优先级越高）
                const priority = maxDistance - distance;
                blockPriorities.push({ blockId, priority, distance });
            }
        }

        // 更新可见块集合
        this.visibleBlocks = newVisibleBlocks;

        // 按距离排序（近的优先）
        blockPriorities.sort((a, b) => b.priority - a.priority);
        const blocksToLoad = blockPriorities.map(item => item.blockId);

        // 请求加载新的可见块
        this.dataLoader.requestBlocks(blocksToLoad);

        // 更新状态（对应原始代码的状态管理）
        this.updateLastState(this.currentCamera);
        this.needsUpdate = false;

        console.log(`LOD Update: ${blocksToLoad.length} blocks visible, detail: ${this.detail}`);
    }

    /**
     * 估算块的中心位置（简化版本）
     */
    estimateBlockCenter(blockId, sceneRadius) {
        // 简单的网格分布估算
        const blocksPerSide = Math.ceil(Math.cbrt(this.dataLoader.blockCount));
        const x = (blockId % blocksPerSide) - blocksPerSide / 2;
        const y = Math.floor(blockId / blocksPerSide) % blocksPerSide - blocksPerSide / 2;
        const z = Math.floor(blockId / (blocksPerSide * blocksPerSide)) - blocksPerSide / 2;

        const scale = sceneRadius / blocksPerSide;
        return [x * scale, y * scale, z * scale];
    }



    /**
     * 获取当前可见块
     */
    getVisibleBlocks() {
        return Array.from(this.visibleBlocks);
    }

    /**
     * 获取LOD统计信息
     */
    getStats() {
        return {
            visibleBlocks: this.visibleBlocks.size,
            totalBlocks: this.dataLoader.blockCount,
            detail: this.detail,
            loadedBlocks: this.dataLoader.loadedCount,
            needsUpdate: this.needsUpdate
        };
    }

    /**
     * 检查相机是否发生显著变化（对应原始代码的变化检测）
     */
    hasCameraChanged(camera) {
        const cameraPos = [camera.position.x, camera.position.y, camera.position.z];
        const threshold = 0.001; // 对应原始代码的0.001阈值

        // 检查位置变化
        const posChanged = Math.abs(cameraPos[0] - this.lastEye[0]) > threshold ||
                          Math.abs(cameraPos[1] - this.lastEye[1]) > threshold ||
                          Math.abs(cameraPos[2] - this.lastEye[2]) > threshold;

        // 检查细节级别变化
        const detailChanged = this.detail !== this.lastDetail;

        return posChanged || detailChanged;
    }

    /**
     * 更新上次状态
     */
    updateLastState(camera) {
        const cameraPos = [camera.position.x, camera.position.y, camera.position.z];
        this.lastEye = [...cameraPos];
        this.lastDetail = this.detail;
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.visibleBlocks.clear();
        this.currentCamera = null;
    }
}
