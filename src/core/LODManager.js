/**
 * Splatter.app Viewer - LOD Manager (基于原始代码的receiveLod机制)
 * 负责管理细节级别和块的可见性判断
 */

export class LODManager {
    constructor(dataLoader) {
        this.dataLoader = dataLoader;
        this.currentCamera = null;
        this.detail = 1.0;  // 细节参数，对应原始代码的detail
        this.visibleBlocks = new Set();
        this.lastUpdateTime = 0;
        this.updateThreshold = 100; // 100ms更新间隔
    }

    /**
     * 设置相机参数
     */
    setCamera(camera) {
        this.currentCamera = camera;
        this.requestUpdate();
    }

    /**
     * 设置细节级别
     */
    setDetail(detail) {
        this.detail = Math.max(0.1, Math.min(2.0, detail));
        this.requestUpdate();
    }

    /**
     * 请求更新LOD
     */
    requestUpdate() {
        const now = Date.now();
        if (now - this.lastUpdateTime < this.updateThreshold) {
            return;
        }
        this.lastUpdateTime = now;
        
        if (this.currentCamera && this.dataLoader.blockCount > 0) {
            this.updateLOD();
        }
    }

    /**
     * 更新LOD - 基于原始代码的简单距离判断
     */
    updateLOD() {
        const camera = this.currentCamera;
        const cameraPos = [camera.position.x, camera.position.y, camera.position.z];
        const sceneRadius = this.dataLoader.root.radius;
        const blockSize = this.dataLoader.blockSize;
        
        // 计算基础加载距离
        const baseDistance = sceneRadius * this.detail;
        const maxDistance = baseDistance * 2;
        
        const newVisibleBlocks = new Set();
        
        // 简单的块可见性判断
        for (let blockId = 0; blockId < this.dataLoader.blockCount; blockId++) {
            // 估算块的中心位置（简化版本）
            const blockCenter = this.estimateBlockCenter(blockId, sceneRadius);
            const distance = this.dataLoader.calculateDistance(cameraPos, blockCenter);
            
            // 基于距离和细节参数判断是否需要加载
            if (distance < maxDistance) {
                newVisibleBlocks.add(blockId);
            }
        }
        
        // 更新可见块集合
        this.visibleBlocks = newVisibleBlocks;
        
        // 请求加载新的可见块
        const blocksToLoad = Array.from(this.visibleBlocks);
        this.dataLoader.requestBlocks(blocksToLoad);
        
        console.log(`LOD Update: ${blocksToLoad.length} blocks visible, detail: ${this.detail}`);
    }

    /**
     * 估算块的中心位置（简化版本）
     */
    estimateBlockCenter(blockId, sceneRadius) {
        // 简单的网格分布估算
        const blocksPerSide = Math.ceil(Math.cbrt(this.dataLoader.blockCount));
        const x = (blockId % blocksPerSide) - blocksPerSide / 2;
        const y = Math.floor(blockId / blocksPerSide) % blocksPerSide - blocksPerSide / 2;
        const z = Math.floor(blockId / (blocksPerSide * blocksPerSide)) - blocksPerSide / 2;
        
        const scale = sceneRadius / blocksPerSide;
        return [x * scale, y * scale, z * scale];
    }

    /**
     * 获取当前可见块
     */
    getVisibleBlocks() {
        return Array.from(this.visibleBlocks);
    }

    /**
     * 获取LOD统计信息
     */
    getStats() {
        return {
            visibleBlocks: this.visibleBlocks.size,
            totalBlocks: this.dataLoader.blockCount,
            detail: this.detail,
            loadedBlocks: this.dataLoader.loadedCount
        };
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.visibleBlocks.clear();
        this.currentCamera = null;
    }
}
