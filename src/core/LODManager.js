/**
 * Splatter.app Viewer - LOD Manager (增强版)
 * 负责管理细节级别、视锥剔除和加载优先级
 */

export class LODManager {
    constructor(dataLoader) {
        this.dataLoader = dataLoader;
        this.currentCamera = null;
        this.detail = 1.0;  // 细节参数，对应原始代码的detail

        // LOD级别管理
        this.blockLODLevels = new Map(); // blockId -> LOD级别 (0=最高, 1=中等, 2=最低)
        this.visibleBlocks = new Set();
        this.frustumBlocks = new Set(); // 视锥内的块

        // 更新控制
        this.lastUpdateTime = 0;
        this.updateThreshold = 100; // 100ms更新间隔

        // LOD距离阈值
        this.lodDistances = [
            0.5,  // 高精度距离阈值
            1.0,  // 中精度距离阈值
            2.0   // 低精度距离阈值
        ];
    }

    /**
     * 设置相机参数
     */
    setCamera(camera) {
        this.currentCamera = camera;
        this.requestUpdate();
    }

    /**
     * 设置细节级别
     */
    setDetail(detail) {
        this.detail = Math.max(0.1, Math.min(2.0, detail));
        this.requestUpdate();
    }

    /**
     * 请求更新LOD
     */
    requestUpdate() {
        const now = Date.now();
        if (now - this.lastUpdateTime < this.updateThreshold) {
            return;
        }
        this.lastUpdateTime = now;

        if (this.currentCamera && this.dataLoader.blockCount > 0) {
            this.updateLOD();
        }
    }

    /**
     * 更新LOD - 支持视锥剔除和LOD级别
     */
    updateLOD() {
        const camera = this.currentCamera;
        const cameraPos = [camera.position.x, camera.position.y, camera.position.z];
        const sceneRadius = this.dataLoader.root.radius;

        // 计算基础距离阈值
        const baseDistance = sceneRadius * this.detail;
        const maxDistance = baseDistance * this.lodDistances[2]; // 最远距离

        const newVisibleBlocks = new Set();
        const newFrustumBlocks = new Set();
        const blockPriorities = []; // 用于优先级排序

        // 遍历所有块进行LOD计算
        for (let blockId = 0; blockId < this.dataLoader.blockCount; blockId++) {
            const blockCenter = this.estimateBlockCenter(blockId, sceneRadius);
            const distance = this.dataLoader.calculateDistance(cameraPos, blockCenter);
            const normalizedDistance = distance / baseDistance;

            // 视锥剔除检查
            const inFrustum = this.isBlockInFrustum(blockCenter, camera);
            if (inFrustum) {
                newFrustumBlocks.add(blockId);
            }

            // 距离剔除
            if (distance > maxDistance) {
                // 超出最大距离，标记为不可见
                this.blockLODLevels.delete(blockId);
                continue;
            }

            // 计算LOD级别
            let lodLevel = 2; // 默认最低精度
            if (normalizedDistance < this.lodDistances[0]) {
                lodLevel = 0; // 高精度
            } else if (normalizedDistance < this.lodDistances[1]) {
                lodLevel = 1; // 中精度
            }

            // 视锥外的块降低一个LOD级别（变粗糙）
            if (!inFrustum) {
                lodLevel = Math.min(lodLevel + 1, 2);
            }

            // 更新块的LOD级别
            this.blockLODLevels.set(blockId, lodLevel);
            newVisibleBlocks.add(blockId);

            // 计算加载优先级（距离越近，视锥内优先级越高）
            const priority = this.calculateBlockPriority(distance, inFrustum, lodLevel);
            blockPriorities.push({ blockId, priority, lodLevel, distance });
        }

        // 更新集合
        this.visibleBlocks = newVisibleBlocks;
        this.frustumBlocks = newFrustumBlocks;

        // 按优先级排序并请求加载
        blockPriorities.sort((a, b) => b.priority - a.priority);
        const blocksToLoad = blockPriorities.map(item => ({
            blockId: item.blockId,
            lodLevel: item.lodLevel,
            priority: item.priority
        }));

        this.dataLoader.requestBlocksWithPriority(blocksToLoad);

        console.log(`LOD Update: ${newVisibleBlocks.size} visible, ${newFrustumBlocks.size} in frustum, detail: ${this.detail}`);
    }

    /**
     * 估算块的中心位置（简化版本）
     */
    estimateBlockCenter(blockId, sceneRadius) {
        // 简单的网格分布估算
        const blocksPerSide = Math.ceil(Math.cbrt(this.dataLoader.blockCount));
        const x = (blockId % blocksPerSide) - blocksPerSide / 2;
        const y = Math.floor(blockId / blocksPerSide) % blocksPerSide - blocksPerSide / 2;
        const z = Math.floor(blockId / (blocksPerSide * blocksPerSide)) - blocksPerSide / 2;

        const scale = sceneRadius / blocksPerSide;
        return [x * scale, y * scale, z * scale];
    }

    /**
     * 简化的视锥剔除检查
     */
    isBlockInFrustum(blockCenter, camera) {
        // 获取相机的视图方向
        const cameraPos = [camera.position.x, camera.position.y, camera.position.z];
        const forward = this.getCameraForward(camera);

        // 计算块到相机的向量
        const toBlock = [
            blockCenter[0] - cameraPos[0],
            blockCenter[1] - cameraPos[1],
            blockCenter[2] - cameraPos[2]
        ];

        // 简化的视锥检查：计算点积判断是否在视野前方
        const dot = toBlock[0] * forward[0] + toBlock[1] * forward[1] + toBlock[2] * forward[2];

        // 如果点积为正，说明在相机前方
        // 这里可以加入更复杂的视锥体检查，但为了简化先用这个
        return dot > 0;
    }

    /**
     * 获取相机前向向量（简化版本）
     */
    getCameraForward(camera) {
        // 这里需要根据实际的相机实现来获取前向向量
        // 简化版本：假设相机朝向-Z方向
        return [0, 0, -1];
    }

    /**
     * 计算块的加载优先级
     */
    calculateBlockPriority(distance, inFrustum, lodLevel) {
        let priority = 1000; // 基础优先级

        // 距离越近优先级越高
        priority -= distance * 10;

        // 视锥内的块优先级更高
        if (inFrustum) {
            priority += 500;
        }

        // LOD级别越高（数值越小）优先级越高
        priority += (2 - lodLevel) * 100;

        return Math.max(0, priority);
    }

    /**
     * 获取当前可见块
     */
    getVisibleBlocks() {
        return Array.from(this.visibleBlocks);
    }

    /**
     * 获取LOD统计信息
     */
    getStats() {
        return {
            visibleBlocks: this.visibleBlocks.size,
            totalBlocks: this.dataLoader.blockCount,
            detail: this.detail,
            loadedBlocks: this.dataLoader.loadedCount
        };
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.visibleBlocks.clear();
        this.currentCamera = null;
    }
}
