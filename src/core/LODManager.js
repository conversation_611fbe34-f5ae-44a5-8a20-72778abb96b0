/**
 * Splatter.app Viewer - LOD Manager
 * 专门管理Level of Detail系统的类
 */

import { vec3 } from '../utils/Math.js';

export class LODManager {
    constructor(sceneRadius, totalSplats, blockSize) {
        this.sceneRadius = sceneRadius;
        this.totalSplats = totalSplats;
        this.baseBlockSize = blockSize;
        
        // LOD配置
        this.lodLevels = [];
        this.distanceThresholds = [];
        this.blockSizes = [];
        this.qualityFactors = [1.0, 0.75, 0.5, 0.25]; // 不同LOD级别的质量因子
        
        // 性能监控
        this.performanceMetrics = {
            frameTime: 16.67, // 目标60fps
            memoryUsage: 0,
            renderTime: 0
        };
        
        // 自适应LOD参数
        this.adaptiveLOD = true;
        this.performanceTarget = 60; // 目标FPS
        this.lodBias = 0; // LOD偏移，正值降低质量，负值提高质量
        
        this.initializeLODLevels();
    }
    
    /**
     * 初始化LOD级别
     */
    initializeLODLevels() {
        // 根据场景大小动态计算距离阈值
        this.distanceThresholds = [
            this.sceneRadius * 0.1,  // LOD 0: 高细节
            this.sceneRadius * 0.3,  // LOD 1: 中等细节
            this.sceneRadius * 0.8,  // LOD 2: 低细节
            this.sceneRadius * 2.0   // LOD 3: 最低细节
        ];
        
        // 计算不同LOD级别的块大小
        const baseLodSize = Math.min(this.baseBlockSize, Math.max(256, Math.floor(this.totalSplats / 1000)));
        this.blockSizes = [
            baseLodSize,           // LOD 0
            baseLodSize * 2,       // LOD 1
            baseLodSize * 4,       // LOD 2
            baseLodSize * 8        // LOD 3
        ];
        
        // 初始化LOD级别数据
        this.lodLevels = this.distanceThresholds.map((threshold, index) => ({
            level: index,
            distanceThreshold: threshold,
            blockSize: this.blockSizes[index],
            qualityFactor: this.qualityFactors[index],
            loadedBlocks: new Set(),
            activeBlocks: new Set(),
            renderCount: 0,
            memoryUsage: 0
        }));
        
        console.log('LOD Manager initialized:', {
            levels: this.lodLevels.length,
            thresholds: this.distanceThresholds,
            blockSizes: this.blockSizes
        });
    }
    
    /**
     * 计算块的LOD级别
     */
    calculateLODLevel(cameraPos, blockBounds, screenSize) {
        const distance = vec3.distance(cameraPos, blockBounds.center);
        const blockRadius = blockBounds.radius;
        
        // 计算块在屏幕上的投影大小
        const screenProjection = this.calculateScreenProjection(distance, blockRadius, screenSize);
        
        // 根据距离和屏幕投影确定LOD级别
        let lodLevel = this.lodLevels.length - 1;
        
        for (let i = 0; i < this.distanceThresholds.length; i++) {
            const adjustedThreshold = this.distanceThresholds[i] * (1 + this.lodBias * 0.1);
            
            if (distance <= adjustedThreshold && screenProjection > this.getMinScreenSize(i)) {
                lodLevel = i;
                break;
            }
        }
        
        // 自适应LOD调整
        if (this.adaptiveLOD) {
            lodLevel = this.adjustLODForPerformance(lodLevel);
        }
        
        return Math.max(0, Math.min(lodLevel, this.lodLevels.length - 1));
    }
    
    /**
     * 计算屏幕投影大小
     */
    calculateScreenProjection(distance, radius, screenSize) {
        // 简化的透视投影计算
        const fov = Math.PI / 3; // 60度视野
        const projectedRadius = (radius * screenSize.height) / (2 * distance * Math.tan(fov / 2));
        return projectedRadius * 2; // 直径
    }
    
    /**
     * 获取LOD级别的最小屏幕尺寸要求
     */
    getMinScreenSize(lodLevel) {
        const minSizes = [32, 16, 8, 4]; // 像素
        return minSizes[lodLevel] || 1;
    }
    
    /**
     * 根据性能调整LOD
     */
    adjustLODForPerformance(baseLodLevel) {
        const currentFPS = 1000 / this.performanceMetrics.frameTime;
        const fpsRatio = currentFPS / this.performanceTarget;
        
        if (fpsRatio < 0.8) {
            // 性能不足，降低质量
            return Math.min(baseLodLevel + 1, this.lodLevels.length - 1);
        } else if (fpsRatio > 1.2) {
            // 性能充足，可以提高质量
            return Math.max(baseLodLevel - 1, 0);
        }
        
        return baseLodLevel;
    }
    
    /**
     * 更新性能指标
     */
    updatePerformanceMetrics(frameTime, memoryUsage, renderTime) {
        // 使用指数移动平均来平滑性能指标
        const alpha = 0.1;
        this.performanceMetrics.frameTime = this.performanceMetrics.frameTime * (1 - alpha) + frameTime * alpha;
        this.performanceMetrics.memoryUsage = memoryUsage;
        this.performanceMetrics.renderTime = this.performanceMetrics.renderTime * (1 - alpha) + renderTime * alpha;
        
        // 根据性能自动调整LOD偏移
        if (this.adaptiveLOD) {
            this.autoAdjustLODBias();
        }
    }
    
    /**
     * 自动调整LOD偏移
     */
    autoAdjustLODBias() {
        const currentFPS = 1000 / this.performanceMetrics.frameTime;
        const fpsRatio = currentFPS / this.performanceTarget;
        
        if (fpsRatio < 0.7) {
            this.lodBias = Math.min(this.lodBias + 0.1, 2.0);
        } else if (fpsRatio > 1.3) {
            this.lodBias = Math.max(this.lodBias - 0.1, -1.0);
        }
    }
    
    /**
     * 获取LOD统计信息
     */
    getStats() {
        const totalLoadedBlocks = this.lodLevels.reduce((sum, lod) => sum + lod.loadedBlocks.size, 0);
        const totalActiveBlocks = this.lodLevels.reduce((sum, lod) => sum + lod.activeBlocks.size, 0);
        const totalMemoryUsage = this.lodLevels.reduce((sum, lod) => sum + lod.memoryUsage, 0);
        
        return {
            lodLevels: this.lodLevels.map(lod => ({
                level: lod.level,
                distanceThreshold: lod.distanceThreshold,
                blockSize: lod.blockSize,
                qualityFactor: lod.qualityFactor,
                loadedBlocks: lod.loadedBlocks.size,
                activeBlocks: lod.activeBlocks.size,
                renderCount: lod.renderCount,
                memoryUsage: lod.memoryUsage
            })),
            totals: {
                loadedBlocks: totalLoadedBlocks,
                activeBlocks: totalActiveBlocks,
                memoryUsage: totalMemoryUsage
            },
            performance: {
                currentFPS: 1000 / this.performanceMetrics.frameTime,
                targetFPS: this.performanceTarget,
                lodBias: this.lodBias,
                adaptiveLOD: this.adaptiveLOD
            }
        };
    }
    
    /**
     * 重置LOD统计
     */
    resetStats() {
        this.lodLevels.forEach(lod => {
            lod.renderCount = 0;
            lod.memoryUsage = 0;
        });
    }
    
    /**
     * 设置LOD偏移
     */
    setLODBias(bias) {
        this.lodBias = Math.max(-2.0, Math.min(2.0, bias));
    }
    
    /**
     * 启用/禁用自适应LOD
     */
    setAdaptiveLOD(enabled) {
        this.adaptiveLOD = enabled;
        if (!enabled) {
            this.lodBias = 0;
        }
    }
    
    /**
     * 设置性能目标
     */
    setPerformanceTarget(targetFPS) {
        this.performanceTarget = Math.max(30, Math.min(120, targetFPS));
    }
    
    /**
     * 获取推荐的渲染设置
     */
    getRecommendedSettings() {
        const currentFPS = 1000 / this.performanceMetrics.frameTime;
        const memoryPressure = this.performanceMetrics.memoryUsage / (1024 * 1024 * 1024); // GB
        
        return {
            maxConcurrentLoads: currentFPS > 45 ? 8 : 4,
            enableShadows: currentFPS > 50 && memoryPressure < 2,
            enableAntialiasing: currentFPS > 55,
            renderScale: currentFPS < 40 ? 0.8 : 1.0,
            lodBias: this.lodBias
        };
    }
}
