/**
 * Splatter.app Viewer - Simple Data Loader (基于原始代码逻辑)
 * 
 * 原始代码分析总结：
 * 1. 类B是主要的数据加载器，负责元数据和块数据的加载
 * 2. 使用简单的队列机制控制并发加载，没有复杂的LOD系统
 * 3. 主要依赖距离判断和简单的可见性检查来决定加载优先级
 * 4. 数据以固定大小的块进行组织，每个块包含固定数量的散点
 * 5. 使用Worker进行数据处理，但逻辑相对简单
 */

import { WorkerManager } from './WorkerManager.js';

const DATA_BASE_URL = 'https://data.splatter.app';

export class SimpleDataLoader {
    constructor(config, maxConcurrentLoads = 6) {
        // 基础配置（对应原始代码的构造函数）
        this.splatId = config.splatId ?? null;
        this.defaultView = config.defaultView ?? [0, 0, 0, 0, 0, 1];
        this.upDirection = config.upDirection ?? null;
        this.backgroundColor = config.backgroundColor ?? [0, 0, 0, 1];
        
        // 数据源URL（对应原始代码的this.D）
        this.baseUrl = this.determineBaseUrl();
        
        // 核心数据属性（对应原始代码的关键属性）
        this.size = 0;                    // 总散点数（对应this.size）
        this.ratio = 1.0;                 // 比例（对应this.ratio）
        this.root = null;                 // 根节点信息（对应this.root）
        this.blockSize = 0;               // 块大小（对应this.blockSize）
        this.colorMap = [1, 1, 1, 1];     // 颜色映射（对应this.colorMap）
        this.filter2d = 0;                // 2D过滤（对应this.filter2d）
        
        // 分块管理（对应原始代码的分块逻辑）
        this.chunkSize = 4096;            // 分片大小（对应this.t）
        this.chunksPerBlock = 0;          // 每块分片数（对应this.R）
        this.totalChunks = 0;             // 总分片数（对应this.F）
        this.blockCount = 0;              // 块数量（对应this.h）
        
        // 加载状态管理（对应原始代码的加载管理）
        this.loadQueue = [];              // 加载队列（对应this.G）
        this.loadingSet = new Set();      // 正在加载的集合（对应this.N）
        this.maxConcurrentLoads = maxConcurrentLoads; // 最大并发数（对应this.M）
        this.loadedBlocks = null;         // 已加载块状态（对应this.Y）
        this.loadedCount = 0;             // 已加载计数（对应this.S）
        
        // 回调函数（对应原始代码的回调）
        this.onBlockLoaded = () => {};    // 块加载完成回调（对应this.U）
        
        // Worker管理（对应原始代码的this.H）
        this.workerManager = null;
        
        // 初始化Promise（对应原始代码的ready）
        this.ready = this.initialize();
    }
    
    /**
     * 确定基础URL（对应原始代码的URL确定逻辑）
     */
    determineBaseUrl() {
        if (this.splatId) {
            return `${DATA_BASE_URL}/${this.splatId}`;
        }
        
        const urlParams = new URLSearchParams(document.location.search);
        if (urlParams.has('id')) {
            this.splatId = urlParams.get('id');
            return `${DATA_BASE_URL}/${this.splatId}`;
        } else if (urlParams.has('src')) {
            return `/${urlParams.get('src')}`;
        }
        
        return '/';
    }
    
    /**
     * 初始化流程（对应原始代码的初始化逻辑）
     */
    async initialize() {
        try {
            // 1. 加载元数据
            const metadata = await this.loadMetadata(`${this.baseUrl}/meta`);
            
            // 2. 处理元数据
            this.processMetadata(metadata);
            
            // 3. 初始化Worker
            this.workerManager = new WorkerManager(this.chunkSize, this.chunksPerBlock, 4);
            
            console.log('SimpleDataLoader initialized successfully');
            return this;
        } catch (error) {
            console.error('Failed to initialize SimpleDataLoader:', error);
            throw error;
        }
    }
    
    /**
     * 加载元数据（对应原始代码的元数据加载）
     */
    async loadMetadata(url) {
        // 检查全局元数据
        if ('meta' in globalThis) {
            return Promise.resolve(globalThis.meta);
        }
        
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Error fetching ${url}: ${response.status} ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Metadata loading failed:', error);
            throw error;
        }
    }
    
    /**
     * 处理元数据（对应原始代码的元数据处理）
     */
    processMetadata(metadata) {
        // 设置基础属性
        this.size = metadata.size;
        this.ratio = metadata.ratio;
        this.root = {
            size: metadata.root.size,
            radius: metadata.root.radius
        };
        this.blockSize = metadata.block;
        this.colorMap = metadata.colorMap ?? this.colorMap;
        this.filter2d = metadata.filter2d;
        this.upDirection = this.upDirection ?? metadata.up ?? [0, 0, 1];
        
        // 确保颜色映射长度是4的倍数
        while (this.colorMap.length % 4 !== 0) {
            this.colorMap.push(0);
        }
        
        // 计算分块信息
        this.blockCount = Math.ceil(this.size / this.blockSize);
        this.chunksPerBlock = Math.floor(this.blockSize / this.chunkSize);
        this.totalChunks = this.blockCount * this.chunksPerBlock;
        this.loadedBlocks = new Uint8Array(this.blockCount);
        
        console.log('Metadata processed:', {
            size: this.size,
            blockSize: this.blockSize,
            blockCount: this.blockCount,
            chunksPerBlock: this.chunksPerBlock,
            totalChunks: this.totalChunks,
            sceneRadius: this.root.radius
        });
    }
    
    /**
     * 设置加载队列（对应原始代码的setLoadQueue）
     */
    setLoadQueue(queue, callback) {
        this.loadQueue = [...queue]; // 复制队列
        this.onBlockLoaded = callback;
        this.processLoadQueue();
    }
    
    /**
     * 处理加载队列（对应原始代码的核心加载逻辑）
     */
    processLoadQueue() {
        // 检查是否有待加载的块
        for (const blockId of this.loadQueue) {
            // 检查并发限制
            if (this.loadingSet.size >= this.maxConcurrentLoads) {
                break;
            }
            
            // 检查是否已加载或正在加载
            if (!this.loadedBlocks[blockId] && !this.loadingSet.has(blockId)) {
                this.startBlockLoad(blockId);
            }
        }
    }
    
    /**
     * 开始加载单个块（对应原始代码的块加载逻辑）
     */
    startBlockLoad(blockId) {
        this.loadingSet.add(blockId);
        
        this.loadBlock(blockId)
            .then(data => {
                // 加载成功
                this.loadingSet.delete(blockId);
                this.loadedBlocks[blockId] = 1;
                this.loadedCount++;
                
                // 调用回调
                this.onBlockLoaded(blockId, data);
                
                // 继续处理队列
                this.processLoadQueue();
            })
            .catch(error => {
                // 加载失败
                this.loadingSet.delete(blockId);
                console.error(`Failed to load block ${blockId}:`, error);
                
                // 简单重试机制
                setTimeout(() => {
                    this.processLoadQueue();
                }, 1000);
            });
    }
    
    /**
     * 加载单个数据块（对应原始代码的块数据加载）
     */
    async loadBlock(blockId) {
        const url = `${this.baseUrl}/${blockId}`;
        
        try {
            // 获取二进制数据
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const arrayBuffer = await response.arrayBuffer();
            
            // 使用Worker处理数据（如果可用）
            if (this.workerManager) {
                return await this.workerManager.processBlock(arrayBuffer);
            }
            
            // 直接返回原始数据
            return arrayBuffer;
        } catch (error) {
            console.error(`Block ${blockId} load failed:`, error);
            throw error;
        }
    }
    
    /**
     * 获取加载进度（对应原始代码的进度计算）
     */
    getLoadProgress() {
        return {
            loaded: this.loadedCount,
            total: this.blockCount,
            percentage: this.blockCount > 0 ? (this.loadedCount / this.blockCount) * 100 : 0
        };
    }
    
    /**
     * 预加载指定块（对应原始代码的预加载逻辑）
     */
    preloadBlocks(blockIds) {
        const newBlocks = blockIds.filter(id => 
            !this.loadedBlocks[id] && !this.loadingSet.has(id)
        );
        
        // 将新块添加到队列前端（高优先级）
        this.loadQueue = [...new Set([...newBlocks, ...this.loadQueue])];
        this.processLoadQueue();
    }
    
    /**
     * 计算块的优先级（简化的距离计算）
     */
    calculateBlockPriority(blockId, cameraPos) {
        // 简化的块中心计算
        const blockCenter = this.estimateBlockCenter(blockId);
        
        // 计算距离
        const dx = cameraPos[0] - blockCenter[0];
        const dy = cameraPos[1] - blockCenter[1];
        const dz = cameraPos[2] - blockCenter[2];
        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        
        // 距离越近优先级越高
        return 1000 / (distance + 1);
    }
    
    /**
     * 估算块的中心位置（简化实现）
     */
    estimateBlockCenter(blockId) {
        // 简化的空间分布估算
        const blocksPerAxis = Math.ceil(Math.cbrt(this.blockCount));
        const blockWorldSize = (this.root.radius * 2) / blocksPerAxis;
        
        const x = blockId % blocksPerAxis;
        const y = Math.floor(blockId / blocksPerAxis) % blocksPerAxis;
        const z = Math.floor(blockId / (blocksPerAxis * blocksPerAxis));
        
        return [
            -this.root.radius + (x + 0.5) * blockWorldSize,
            -this.root.radius + (y + 0.5) * blockWorldSize,
            -this.root.radius + (z + 0.5) * blockWorldSize
        ];
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            splatId: this.splatId,
            size: this.size,
            blockSize: this.blockSize,
            blockCount: this.blockCount,
            chunkSize: this.chunkSize,
            chunksPerBlock: this.chunksPerBlock,
            totalChunks: this.totalChunks,
            loadedBlocks: this.loadedCount,
            loadingBlocks: this.loadingSet.size,
            queuedBlocks: this.loadQueue.length,
            loadProgress: this.getLoadProgress()
        };
    }
    
    /**
     * 清理资源
     */
    cleanup() {
        if (this.workerManager) {
            this.workerManager.terminate();
        }
        this.loadQueue = [];
        this.loadingSet.clear();
    }
}
