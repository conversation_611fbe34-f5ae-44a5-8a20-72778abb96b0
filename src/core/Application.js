/**
 * Splatter.app Viewer - Main Application Class
 * 主应用程序类，负责协调所有子系统
 */

import { DataLoader } from './DataLoader.js';
import { Renderer } from '../rendering/Renderer.js';
import { Camera } from '../rendering/Camera.js';
import { Controls } from '../controls/Controls.js';
import { UIManager } from '../ui/UIManager.js';
import { isMobile } from '../utils/Utils.js';

export class Application {
    constructor() {
        // 解析URL参数和配置
        this.config = window.config ?? {};
        this.urlParams = this.parseURLParams();
        
        // 基础设置
        this.scale = 1;
        this.fov = this.urlParams.fov ?? 50;
        
        // 初始化核心组件
        this.canvas = null;
        this.gl = this.initWebGL();
        this.dataset = new DataLoader(this.config, 6);
        this.camera = new Camera(this.fov);
        this.renderer = new Renderer(this.gl, this.dataset);
        this.controls = new Controls(this.canvas);
        this.ui = new UIManager(this, this.canvas);
        
        // 设置事件监听
        this.setupEventListeners();
        
        // 初始化完成后的设置
        this.dataset.ready.then(() => {
            this.controls.setUp(this.dataset.upDirection);
            this.controls.setDefaultView(this.dataset.defaultView);
            this.controls.autoRotate();
            this.renderer.setBackgroundColor(this.dataset.backgroundColor);
            this.controls.lastUpdateTime = performance.now();
            this.update();
        });
        
        // 动画循环
        this.lastFrameTime = document.timeline.currentTime;
        this.isAnimating = false;
        this.update();
    }
    
    /**
     * 解析URL参数
     */
    parseURLParams() {
        const params = {};
        const urlParams = new URLSearchParams(window.location.search);
        
        for (const [key, value] of urlParams.entries()) {
            if (value.trim() === '' || isNaN(value)) {
                params[key] = value;
            } else {
                params[key] = Number(value);
            }
        }
        
        return params;
    }
    
    /**
     * 初始化WebGL上下文
     */
    initWebGL() {
        this.canvas = document.createElement('canvas');
        document.body.appendChild(this.canvas);
        
        // 设置canvas样式
        const style = this.canvas.style;
        style.width = '100vw';
        style.height = '100vh';
        style.touchAction = 'none';
        
        // 获取WebGL2上下文
        const gl = this.canvas.getContext('webgl2', {
            antialias: false,
            alpha: true,
            powerPreference: 'high-performance'
        });
        
        if (!gl) {
            throw new Error('WebGL2 not supported.');
        }
        
        return gl;
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 渲染更新回调
        this.renderer.onupdate = this.update.bind(this);
        this.controls.onupdate = this.update.bind(this);
        this.controls.raycast = this.raycast.bind(this);
        
        // 窗口事件
        window.addEventListener('resize', this.update.bind(this));
        document.addEventListener('keyup', this.handleKeyUp.bind(this));
    }
    
    /**
     * 主更新循环
     */
    update() {
        if (this.isAnimating) return;
        this.isAnimating = true;
        
        requestAnimationFrame(() => {
            try {
                // 更新画布尺寸
                const aspectRatio = this.resize();
                
                // 更新相机
                this.camera.setAspectRatio(aspectRatio);
                this.camera.updateMatrices();
                
                // 渲染场景
                this.renderer.render(this.camera, this.canvas.width, this.canvas.height);
                
                // 更新控制器
                this.controls.update();
                
            } catch (error) {
                console.error('Render error:', error);
            } finally {
                this.isAnimating = false;
            }
        });
    }
    
    /**
     * 调整画布尺寸
     */
    resize() {
        const canvas = this.canvas;
        const width = Math.round(canvas.clientWidth * this.scale);
        const height = Math.round(canvas.clientHeight * this.scale);
        
        if (canvas.width !== width || canvas.height !== height) {
            canvas.width = width;
            canvas.height = height;
        }
        
        return width / height;
    }
    
    /**
     * 射线投射 - 用于鼠标拾取
     */
    raycast(x, y) {
        const normalizedX = (x * this.scale) / this.gl.canvas.width;
        const normalizedY = 1 - (y * this.scale) / this.gl.canvas.height;
        
        const depth = this.renderer.getDepthAt(this.camera, [[normalizedX, normalizedY]]);
        return this.camera.raycast(normalizedX, normalizedY, depth);
    }
    
    /**
     * 键盘事件处理
     */
    handleKeyUp(event) {
        switch (event.code) {
            case 'KeyC':
                this.copyViewParams();
                break;
            case 'KeyS':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.saveConfiguration();
                }
                break;
            case 'KeyR':
                this.controls.reset();
                break;
        }
    }
    
    /**
     * 复制视图参数到剪贴板
     */
    copyViewParams() {
        const controls = this.controls;
        const viewParams = [
            controls.center[0], controls.center[1], controls.center[2],
            controls.azimuth - 90, controls.elevation,
            Math.log2(controls.radius)
        ];
        
        const data = JSON.stringify({ pose: viewParams });
        console.log(`Copy view params: ${data}`);
        
        navigator.clipboard.writeText(data).catch(error => {
            console.error(`Error copying view params: ${error}`);
        });
    }
    
    /**
     * 保存配置 (需要配置令牌)
     */
    async saveConfiguration() {
        if (!this.dataset.splatId) return;
        
        const urlParams = new URLSearchParams(document.location.search);
        if (!urlParams.has('configure')) {
            throw new Error('Missing configure token');
        }
        
        const token = urlParams.get('configure');
        
        // 生成缩略图
        const [thumbWidth, thumbHeight] = [1024, 768];
        const [originalWidth, originalHeight] = [this.canvas.width, this.canvas.height];
        
        this.canvas.width = thumbWidth;
        this.canvas.height = thumbHeight;
        this.camera.setAspectRatio(thumbWidth / thumbHeight);
        this.camera.updateMatrices();
        this.renderer.render(this.camera, thumbWidth, thumbHeight);
        
        const thumbnail = this.canvas.toDataURL('image/jpeg', 0.85);
        
        // 恢复原始尺寸
        this.canvas.width = originalWidth;
        this.canvas.height = originalHeight;
        this.update();
        
        // 发送配置数据
        const configData = {
            splatId: this.dataset.splatId,
            defaultView: this.controls.getViewParams(),
            upDirection: this.controls.up,
            backgroundColor: this.renderer.backgroundColor,
            thumbnail: thumbnail,
            token: token
        };
        
        try {
            const response = await fetch('/api/splat/configure', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(configData)
            });
            
            if (!response.ok) {
                console.warn(`Configure API returned status ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.status && (result.status < 200 || result.status >= 300)) {
                throw new Error(result.message);
            }
            
            return result.message;
            
        } catch (error) {
            console.error('Configuration save failed:', error);
            throw error;
        }
    }
}

// 应用程序入口点
window.addEventListener('load', () => {
    try {
        new Application();
    } catch (error) {
        console.error('Application initialization failed:', error);
    }
});
