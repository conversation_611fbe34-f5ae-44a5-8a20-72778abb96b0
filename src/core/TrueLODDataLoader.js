/**
 * Splatter.app Viewer - True LOD Data Loader
 * 基于原始代码真实LOD逻辑的数据加载器
 * 
 * 核心LOD流程分析：
 * 1. 相机变化触发LOD计算请求 (eA方法)
 * 2. Worker计算LOD并返回indices、blocks、offsets
 * 3. receiveLod接收LOD结果并触发块加载
 * 4. 渲染器更新显示内容
 */

import { WorkerManager } from './WorkerManager.js';

const DATA_BASE_URL = 'https://data.splatter.app';

export class TrueLODDataLoader {
    constructor(config, maxConcurrentLoads = 6) {
        // 基础配置
        this.splatId = config.splatId ?? null;
        this.defaultView = config.defaultView ?? [0, 0, 0, 0, 0, 1];
        this.upDirection = config.upDirection ?? null;
        this.backgroundColor = config.backgroundColor ?? [0, 0, 0, 1];
        
        // 数据源URL
        this.baseUrl = this.determineBaseUrl();
        
        // 核心数据属性
        this.size = 0;
        this.ratio = 1.0;
        this.root = null;
        this.blockSize = 0;
        this.colorMap = [1, 1, 1, 1];
        this.filter2d = 0;
        this.chunkSize = 4096;
        this.chunksPerBlock = 0;
        this.totalChunks = 0;
        this.blockCount = 0;
        
        // 加载管理
        this.loadQueue = [];
        this.loadingSet = new Set();
        this.maxConcurrentLoads = maxConcurrentLoads;
        this.loadedBlocks = null;
        this.loadedCount = 0;
        
        // LOD系统核心属性（对应原始代码）
        this.detail = 1.0;                // 细节级别参数（对应this.detail）
        this.lodWorkerBusy = false;       // LOD Worker状态（对应this.cA.yA）
        this.lastEye = [0, 0, 0];         // 上次相机位置（对应this.rA）
        this.lastLook = [0, 0, 0];        // 上次视线方向（对应this.MA）
        this.lastUpdateId = 0;            // 上次更新ID（对应this.YA）
        this.currentUpdateId = 0;         // 当前更新ID（对应this.I）
        
        // LOD结果数据（对应原始代码的LOD缓存）
        this.lodIndices = null;           // 当前LOD索引（对应this.vA）
        this.lodOffsets = null;           // 当前LOD偏移（对应this.zA）
        this.pendingLodIndices = null;    // 待处理LOD索引（对应this.VA）
        this.pendingLodOffsets = null;    // 待处理LOD偏移（对应this.bA）
        this.lodProcessingCount = 0;      // LOD处理计数（对应this.mA）
        
        // 回调函数
        this.onBlockLoaded = () => {};
        this.onLodReceived = () => {};
        this.onUpdate = () => {};
        
        // Worker管理
        this.workerManager = null;
        
        // 初始化
        this.ready = this.initialize();
    }
    
    /**
     * 确定基础URL
     */
    determineBaseUrl() {
        if (this.splatId) {
            return `${DATA_BASE_URL}/${this.splatId}`;
        }
        
        const urlParams = new URLSearchParams(document.location.search);
        if (urlParams.has('id')) {
            this.splatId = urlParams.get('id');
            return `${DATA_BASE_URL}/${this.splatId}`;
        } else if (urlParams.has('src')) {
            return `/${urlParams.get('src')}`;
        }
        
        return '/';
    }
    
    /**
     * 初始化
     */
    async initialize() {
        try {
            const metadata = await this.loadMetadata(`${this.baseUrl}/meta`);
            this.processMetadata(metadata);
            this.workerManager = new WorkerManager(this.chunkSize, this.chunksPerBlock, 4);
            
            // 设置Worker消息处理
            this.setupWorkerHandlers();
            
            console.log('TrueLODDataLoader initialized');
            return this;
        } catch (error) {
            console.error('Failed to initialize TrueLODDataLoader:', error);
            throw error;
        }
    }
    
    /**
     * 加载元数据
     */
    async loadMetadata(url) {
        if ('meta' in globalThis) {
            return Promise.resolve(globalThis.meta);
        }
        
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Error fetching ${url}: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    }
    
    /**
     * 处理元数据
     */
    processMetadata(metadata) {
        this.size = metadata.size;
        this.ratio = metadata.ratio;
        this.root = {
            size: metadata.root.size,
            radius: metadata.root.radius
        };
        this.blockSize = metadata.block;
        this.colorMap = metadata.colorMap ?? this.colorMap;
        this.filter2d = metadata.filter2d;
        this.upDirection = this.upDirection ?? metadata.up ?? [0, 0, 1];
        
        while (this.colorMap.length % 4 !== 0) {
            this.colorMap.push(0);
        }
        
        this.blockCount = Math.ceil(this.size / this.blockSize);
        this.chunksPerBlock = Math.floor(this.blockSize / this.chunkSize);
        this.totalChunks = this.blockCount * this.chunksPerBlock;
        this.loadedBlocks = new Uint8Array(this.blockCount);
        
        console.log('Metadata processed for LOD system:', {
            size: this.size,
            blockCount: this.blockCount,
            sceneRadius: this.root.radius
        });
    }
    
    /**
     * 设置Worker处理器
     */
    setupWorkerHandlers() {
        if (this.workerManager) {
            // 这里应该设置Worker的消息处理，包括LOD计算结果的接收
            this.workerManager.onLodResult = this.receiveLod.bind(this);
        }
    }
    
    /**
     * 请求LOD计算（对应原始代码的eA方法）
     */
    requestLod(camera, screenSize) {
        // 检查是否需要更新LOD
        if (this.lodWorkerBusy) return;
        
        const eye = camera.position;
        const center = camera.target;
        const look = this.calculateLookDirection(eye, center);
        
        // 检查相机是否有显著变化
        if (!this.shouldUpdateLod(eye, look)) return;
        
        // 标记Worker忙碌
        this.lodWorkerBusy = true;
        
        // 发送LOD计算请求到Worker
        const lodRequest = {
            eye: eye,
            look: look,
            center: center,
            focal: (camera.focalLength * screenSize.width) / 2,
            detail: this.detail,
            fov: camera.fov
        };
        
        if (this.workerManager) {
            this.workerManager.requestLod(lodRequest);
        }
        
        // 更新缓存的相机状态
        this.copyVector(eye, this.lastEye);
        this.copyVector(look, this.lastLook);
        this.lastUpdateId = this.currentUpdateId;
        
        console.log('LOD request sent:', lodRequest);
    }
    
    /**
     * 接收LOD结果（对应原始代码的receiveLod方法）
     */
    receiveLod({ indices, blocks, offsets }) {
        console.log('LOD received:', { 
            indicesCount: indices.length, 
            blocksCount: blocks.length,
            offsetsCount: offsets.length 
        });
        
        // 重置Worker忙碌状态
        this.lodWorkerBusy = false;
        
        // 存储LOD结果
        this.lodIndices = indices;
        this.lodOffsets = offsets;
        
        // 触发块加载
        this.loadBlocks(blocks);
        
        // 更新渲染索引数量
        this.updateRenderCount(indices.length);
        
        // 触发更新
        this.triggerUpdate();
        
        // 调用回调
        this.onLodReceived({ indices, blocks, offsets });
    }
    
    /**
     * 加载指定的块
     */
    loadBlocks(blockIds) {
        for (const blockId of blockIds) {
            if (!this.loadedBlocks[blockId] && !this.loadingSet.has(blockId)) {
                this.startBlockLoad(blockId);
            }
        }
    }
    
    /**
     * 开始加载单个块
     */
    startBlockLoad(blockId) {
        if (this.loadingSet.size >= this.maxConcurrentLoads) return;
        
        this.loadingSet.add(blockId);
        
        this.loadBlock(blockId)
            .then(data => {
                this.loadingSet.delete(blockId);
                this.loadedBlocks[blockId] = 1;
                this.loadedCount++;
                
                // 处理块数据
                this.processBlockData(blockId, data);
                
                // 调用回调
                this.onBlockLoaded(blockId, data);
                
                // 增加更新ID
                this.currentUpdateId++;
            })
            .catch(error => {
                this.loadingSet.delete(blockId);
                console.error(`Failed to load block ${blockId}:`, error);
            });
    }
    
    /**
     * 加载单个数据块
     */
    async loadBlock(blockId) {
        const url = `${this.baseUrl}/${blockId}`;
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const arrayBuffer = await response.arrayBuffer();
        
        if (this.workerManager) {
            return await this.workerManager.processBlock(arrayBuffer);
        }
        
        return arrayBuffer;
    }
    
    /**
     * 处理块数据（对应原始代码的LA方法）
     */
    processBlockData(blockId, data) {
        // 发送块数据到渲染Worker
        if (this.workerManager) {
            this.workerManager.sendBlockData({
                index: blockId,
                tree1: data.tree1,
                tree2: data.tree2,
                points: data.points,
                anchors: data.gauss3
            });
        }
        
        this.currentUpdateId++;
    }
    
    /**
     * 调整细节级别（对应原始代码的SA方法）
     */
    adjustDetail(delta) {
        const factor = delta > 0 ? Math.SQRT1_2 : Math.SQRT2;
        this.detail *= factor;
        this.detail = Math.max(this.detail, 0.5);
        this.currentUpdateId++;
        this.triggerUpdate();
        
        console.log(`Detail adjusted to: ${this.detail}`);
    }
    
    /**
     * 计算视线方向
     */
    calculateLookDirection(eye, center) {
        const look = [
            center[0] - eye[0],
            center[1] - eye[1],
            center[2] - eye[2]
        ];
        
        // 标准化
        const length = Math.sqrt(look[0] * look[0] + look[1] * look[1] + look[2] * look[2]);
        if (length > 0) {
            look[0] /= length;
            look[1] /= length;
            look[2] /= length;
        }
        
        return look;
    }
    
    /**
     * 检查是否应该更新LOD
     */
    shouldUpdateLod(eye, look) {
        const eyeDistance = this.vectorDistance(eye, this.lastEye);
        const lookDistance = this.vectorDistance(look, this.lastLook);
        const updateIdChanged = this.currentUpdateId !== this.lastUpdateId;
        
        return eyeDistance > 0.001 || lookDistance > 0.001 || updateIdChanged;
    }
    
    /**
     * 计算向量距离
     */
    vectorDistance(a, b) {
        const dx = a[0] - b[0];
        const dy = a[1] - b[1];
        const dz = a[2] - b[2];
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    /**
     * 复制向量
     */
    copyVector(src, dst) {
        dst[0] = src[0];
        dst[1] = src[1];
        dst[2] = src[2];
    }
    
    /**
     * 更新渲染数量
     */
    updateRenderCount(count) {
        // 这里应该通知渲染器更新要渲染的散点数量
        console.log(`Render count updated to: ${count}`);
    }
    
    /**
     * 触发更新
     */
    triggerUpdate() {
        this.onUpdate();
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            splatId: this.splatId,
            size: this.size,
            blockSize: this.blockSize,
            blockCount: this.blockCount,
            loadedBlocks: this.loadedCount,
            detail: this.detail,
            lodWorkerBusy: this.lodWorkerBusy,
            currentUpdateId: this.currentUpdateId,
            lodIndicesCount: this.lodIndices ? this.lodIndices.length : 0,
            loadProgress: {
                loaded: this.loadedCount,
                total: this.blockCount,
                percentage: this.blockCount > 0 ? (this.loadedCount / this.blockCount) * 100 : 0
            }
        };
    }
    
    /**
     * 清理资源
     */
    cleanup() {
        if (this.workerManager) {
            this.workerManager.terminate();
        }
        this.loadQueue = [];
        this.loadingSet.clear();
    }
}
