/**
 * Splatter.app Viewer - Spatial Manager
 * 管理大场景的空间分块和可见性剔除
 */

import { vec3 } from '../utils/Math.js';

export class SpatialManager {
    constructor(sceneRadius, blockCount) {
        this.sceneRadius = sceneRadius;
        this.blockCount = blockCount;
        
        // 空间结构
        this.octree = null;
        this.blockBounds = new Map();
        this.spatialGrid = null;
        this.gridResolution = 32; // 网格分辨率
        
        // 可见性管理
        this.visibleBlocks = new Set();
        this.frustumCulledBlocks = new Set();
        this.occludedBlocks = new Set();
        
        // 流式加载区域
        this.loadingRadius = sceneRadius * 1.5;
        this.unloadingRadius = sceneRadius * 3.0;
        
        // 性能统计
        this.cullingStats = {
            totalBlocks: 0,
            visibleBlocks: 0,
            frustumCulled: 0,
            occlusionCulled: 0,
            distanceCulled: 0
        };
        
        this.initializeSpatialStructures();
    }
    
    /**
     * 初始化空间结构
     */
    initializeSpatialStructures() {
        // 创建八叉树
        this.octree = new OctreeNode(
            [-this.sceneRadius, -this.sceneRadius, -this.sceneRadius],
            [this.sceneRadius, this.sceneRadius, this.sceneRadius],
            0, 6
        );
        
        // 创建空间网格
        this.spatialGrid = new SpatialGrid(
            [-this.sceneRadius, -this.sceneRadius, -this.sceneRadius],
            [this.sceneRadius, this.sceneRadius, this.sceneRadius],
            this.gridResolution
        );
        
        // 计算块的空间分布
        this.calculateBlockDistribution();
    }
    
    /**
     * 计算块的空间分布
     */
    calculateBlockDistribution() {
        const blocksPerAxis = Math.ceil(Math.cbrt(this.blockCount));
        const blockWorldSize = (this.sceneRadius * 2) / blocksPerAxis;
        
        for (let i = 0; i < this.blockCount; i++) {
            const x = i % blocksPerAxis;
            const y = Math.floor(i / blocksPerAxis) % blocksPerAxis;
            const z = Math.floor(i / (blocksPerAxis * blocksPerAxis));
            
            const min = [
                -this.sceneRadius + x * blockWorldSize,
                -this.sceneRadius + y * blockWorldSize,
                -this.sceneRadius + z * blockWorldSize
            ];
            
            const max = [
                min[0] + blockWorldSize,
                min[1] + blockWorldSize,
                min[2] + blockWorldSize
            ];
            
            const bounds = {
                min,
                max,
                center: [
                    (min[0] + max[0]) * 0.5,
                    (min[1] + max[1]) * 0.5,
                    (min[2] + max[2]) * 0.5
                ],
                radius: blockWorldSize * 0.866, // sqrt(3)/2
                size: blockWorldSize
            };
            
            this.blockBounds.set(i, bounds);
            
            // 插入到空间结构中
            this.octree.insertBlock(i, bounds);
            this.spatialGrid.insertBlock(i, bounds);
        }
    }
    
    /**
     * 更新可见性
     */
    updateVisibility(camera, frustumPlanes, maxDistance = null) {
        const startTime = performance.now();
        
        // 重置统计
        this.cullingStats.totalBlocks = this.blockCount;
        this.cullingStats.visibleBlocks = 0;
        this.cullingStats.frustumCulled = 0;
        this.cullingStats.occlusionCulled = 0;
        this.cullingStats.distanceCulled = 0;
        
        const newVisibleBlocks = new Set();
        const cameraPos = camera.position;
        const maxDist = maxDistance || this.loadingRadius;
        
        // 使用八叉树进行粗略剔除
        const potentiallyVisible = this.octree.queryFrustum(frustumPlanes);
        
        for (const blockId of potentiallyVisible) {
            const bounds = this.blockBounds.get(blockId);
            if (!bounds) continue;
            
            // 距离剔除
            const distance = vec3.distance(cameraPos, bounds.center);
            if (distance > maxDist + bounds.radius) {
                this.cullingStats.distanceCulled++;
                continue;
            }
            
            // 精确视锥体剔除
            if (!this.isBlockInFrustum(bounds, frustumPlanes)) {
                this.cullingStats.frustumCulled++;
                continue;
            }
            
            // 遮挡剔除（简化版本）
            if (this.isBlockOccluded(bounds, cameraPos)) {
                this.cullingStats.occlusionCulled++;
                continue;
            }
            
            newVisibleBlocks.add(blockId);
            this.cullingStats.visibleBlocks++;
        }
        
        this.visibleBlocks = newVisibleBlocks;
        
        const cullingTime = performance.now() - startTime;
        
        return {
            visibleBlocks: newVisibleBlocks,
            stats: { ...this.cullingStats, cullingTime }
        };
    }
    
    /**
     * 检查块是否在视锥体内
     */
    isBlockInFrustum(bounds, frustumPlanes) {
        const center = bounds.center;
        const radius = bounds.radius;
        
        for (const plane of frustumPlanes) {
            const distance = plane[0] * center[0] + plane[1] * center[1] + plane[2] * center[2] + plane[3];
            if (distance < -radius) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 简化的遮挡剔除
     */
    isBlockOccluded(bounds, cameraPos) {
        // 简化实现：检查是否被更近的大块遮挡
        const toCameraDir = vec3.create();
        vec3.subtract(toCameraDir, cameraPos, bounds.center);
        vec3.normalize(toCameraDir, toCameraDir);
        
        const distance = vec3.distance(cameraPos, bounds.center);
        
        // 在相机到块的路径上检查是否有遮挡物
        const stepSize = bounds.radius;
        const steps = Math.floor(distance / stepSize);
        
        for (let i = 1; i < steps; i++) {
            const testPoint = vec3.create();
            vec3.scaleAndAdd(testPoint, bounds.center, toCameraDir, i * stepSize);
            
            // 检查这个点是否在其他更大的块内
            const nearbyBlocks = this.spatialGrid.queryPoint(testPoint);
            for (const nearbyBlockId of nearbyBlocks) {
                const nearbyBounds = this.blockBounds.get(nearbyBlockId);
                if (nearbyBounds && nearbyBounds.radius > bounds.radius * 1.5) {
                    const distToNearby = vec3.distance(testPoint, nearbyBounds.center);
                    if (distToNearby < nearbyBounds.radius) {
                        return true; // 被遮挡
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * 获取需要加载的块
     */
    getBlocksToLoad(cameraPos, loadRadius = null) {
        const radius = loadRadius || this.loadingRadius;
        const blocksToLoad = new Set();
        
        for (const [blockId, bounds] of this.blockBounds) {
            const distance = vec3.distance(cameraPos, bounds.center);
            if (distance <= radius + bounds.radius) {
                blocksToLoad.add(blockId);
            }
        }
        
        return blocksToLoad;
    }
    
    /**
     * 获取需要卸载的块
     */
    getBlocksToUnload(cameraPos, unloadRadius = null) {
        const radius = unloadRadius || this.unloadingRadius;
        const blocksToUnload = new Set();
        
        for (const [blockId, bounds] of this.blockBounds) {
            const distance = vec3.distance(cameraPos, bounds.center);
            if (distance > radius + bounds.radius) {
                blocksToUnload.add(blockId);
            }
        }
        
        return blocksToUnload;
    }
    
    /**
     * 获取块的邻居
     */
    getNeighborBlocks(blockId, radius = 1) {
        const bounds = this.blockBounds.get(blockId);
        if (!bounds) return new Set();
        
        const neighbors = new Set();
        const searchRadius = bounds.radius * radius;
        
        for (const [otherId, otherBounds] of this.blockBounds) {
            if (otherId === blockId) continue;
            
            const distance = vec3.distance(bounds.center, otherBounds.center);
            if (distance <= searchRadius + otherBounds.radius) {
                neighbors.add(otherId);
            }
        }
        
        return neighbors;
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            spatial: {
                totalBlocks: this.blockCount,
                sceneRadius: this.sceneRadius,
                loadingRadius: this.loadingRadius,
                unloadingRadius: this.unloadingRadius
            },
            culling: this.cullingStats,
            octree: this.octree ? this.octree.getStats() : null,
            grid: this.spatialGrid ? this.spatialGrid.getStats() : null
        };
    }
}

/**
 * 空间网格类
 */
class SpatialGrid {
    constructor(min, max, resolution) {
        this.min = min;
        this.max = max;
        this.resolution = resolution;
        this.cellSize = [
            (max[0] - min[0]) / resolution,
            (max[1] - min[1]) / resolution,
            (max[2] - min[2]) / resolution
        ];
        
        this.grid = new Array(resolution * resolution * resolution);
        for (let i = 0; i < this.grid.length; i++) {
            this.grid[i] = new Set();
        }
    }
    
    /**
     * 获取网格索引
     */
    getGridIndex(point) {
        const x = Math.floor((point[0] - this.min[0]) / this.cellSize[0]);
        const y = Math.floor((point[1] - this.min[1]) / this.cellSize[1]);
        const z = Math.floor((point[2] - this.min[2]) / this.cellSize[2]);
        
        const clampedX = Math.max(0, Math.min(this.resolution - 1, x));
        const clampedY = Math.max(0, Math.min(this.resolution - 1, y));
        const clampedZ = Math.max(0, Math.min(this.resolution - 1, z));
        
        return clampedZ * this.resolution * this.resolution + clampedY * this.resolution + clampedX;
    }
    
    /**
     * 插入块
     */
    insertBlock(blockId, bounds) {
        // 计算块覆盖的网格单元
        const minIndex = this.getGridIndex(bounds.min);
        const maxIndex = this.getGridIndex(bounds.max);
        
        // 简化：只插入到中心点所在的网格
        const centerIndex = this.getGridIndex(bounds.center);
        this.grid[centerIndex].add(blockId);
    }
    
    /**
     * 查询点附近的块
     */
    queryPoint(point) {
        const index = this.getGridIndex(point);
        return this.grid[index] || new Set();
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        let totalBlocks = 0;
        let occupiedCells = 0;
        
        for (const cell of this.grid) {
            if (cell.size > 0) {
                occupiedCells++;
                totalBlocks += cell.size;
            }
        }
        
        return {
            resolution: this.resolution,
            totalCells: this.grid.length,
            occupiedCells,
            totalBlocks,
            averageBlocksPerCell: totalBlocks / occupiedCells || 0
        };
    }
}

/**
 * 八叉树节点类（从DataLoader.js移动到这里）
 */
class OctreeNode {
    constructor(min, max, depth = 0, maxDepth = 6) {
        this.min = min;
        this.max = max;
        this.center = [
            (min[0] + max[0]) * 0.5,
            (min[1] + max[1]) * 0.5,
            (min[2] + max[2]) * 0.5
        ];
        this.size = [
            max[0] - min[0],
            max[1] - min[1],
            max[2] - min[2]
        ];
        this.depth = depth;
        this.maxDepth = maxDepth;
        this.children = null;
        this.blocks = new Set();
        this.isLeaf = depth >= maxDepth;
    }
    
    subdivide() {
        if (this.children || this.isLeaf) return;
        
        this.children = [];
        const halfSize = [
            this.size[0] * 0.5,
            this.size[1] * 0.5,
            this.size[2] * 0.5
        ];
        
        for (let i = 0; i < 8; i++) {
            const x = (i & 1) ? 1 : 0;
            const y = (i & 2) ? 1 : 0;
            const z = (i & 4) ? 1 : 0;
            
            const childMin = [
                this.min[0] + x * halfSize[0],
                this.min[1] + y * halfSize[1],
                this.min[2] + z * halfSize[2]
            ];
            
            const childMax = [
                childMin[0] + halfSize[0],
                childMin[1] + halfSize[1],
                childMin[2] + halfSize[2]
            ];
            
            this.children.push(new OctreeNode(childMin, childMax, this.depth + 1, this.maxDepth));
        }
    }
    
    insertBlock(blockId, bounds) {
        if (!this.intersectsBounds(bounds)) return false;
        
        if (this.isLeaf) {
            this.blocks.add(blockId);
            return true;
        }
        
        if (!this.children) this.subdivide();
        
        let inserted = false;
        for (const child of this.children) {
            if (child.insertBlock(blockId, bounds)) {
                inserted = true;
            }
        }
        
        if (!inserted) {
            this.blocks.add(blockId);
        }
        
        return true;
    }
    
    queryFrustum(frustumPlanes, result = new Set()) {
        if (!this.intersectsFrustum(frustumPlanes)) return result;
        
        for (const blockId of this.blocks) {
            result.add(blockId);
        }
        
        if (this.children) {
            for (const child of this.children) {
                child.queryFrustum(frustumPlanes, result);
            }
        }
        
        return result;
    }
    
    intersectsBounds(bounds) {
        return !(
            bounds.max[0] < this.min[0] || bounds.min[0] > this.max[0] ||
            bounds.max[1] < this.min[1] || bounds.min[1] > this.max[1] ||
            bounds.max[2] < this.min[2] || bounds.min[2] > this.max[2]
        );
    }
    
    intersectsFrustum(frustumPlanes) {
        for (const plane of frustumPlanes) {
            const positive = [
                plane[0] >= 0 ? this.max[0] : this.min[0],
                plane[1] >= 0 ? this.max[1] : this.min[1],
                plane[2] >= 0 ? this.max[2] : this.min[2]
            ];
            
            const distance = plane[0] * positive[0] + plane[1] * positive[1] + plane[2] * positive[2] + plane[3];
            if (distance < 0) return false;
        }
        
        return true;
    }
    
    getStats() {
        let nodeCount = 1;
        let leafCount = this.isLeaf ? 1 : 0;
        let blockCount = this.blocks.size;
        
        if (this.children) {
            for (const child of this.children) {
                const childStats = child.getStats();
                nodeCount += childStats.nodeCount;
                leafCount += childStats.leafCount;
                blockCount += childStats.blockCount;
            }
        }
        
        return { nodeCount, leafCount, blockCount };
    }
}
