/**
 * Splatter.app Viewer - UI Manager
 * 基于Vue.js的用户界面管理系统
 */

import { isMobile } from '../utils/Utils.js';

export class UIManager {
    constructor(app, canvas) {
        this.app = app;
        this.canvas = canvas;
        
        // 检查是否为配置模式
        const urlParams = new URLSearchParams(document.location.search);
        this.isConfigMode = urlParams.has('configure');
        
        // 初始化Vue应用
        this.initVueApp();
        
        // 设置事件监听
        this.setupEventListeners();
        
        // 如果是配置模式，显示配置背景
        if (this.isConfigMode) {
            this.showConfigBackground();
        }
    }
    
    /**
     * 初始化Vue应用
     */
    initVueApp() {
        const { createApp, reactive, computed } = Vue;
        
        // 创建响应式数据
        const controls = this.app.controls;
        const renderer = this.app.renderer;
        
        controls.up = reactive(controls.up);
        renderer.backgroundColor = reactive(renderer.backgroundColor);
        
        // 创建Vue应用
        const vueApp = createApp({
            data: () => ({
                // 控制数据
                up: controls.up,
                backgroundColor: renderer.backgroundColor,
                
                // UI状态
                configMode: this.isConfigMode,
                resolution: 'low',
                saving: false,
                isFullscreen: false,
                isMobile: isMobile(),
                
                // 提示信息
                alertMessage: '',
                alertClass: '',
                
                // 自定义上方向
                wantCustom: false,
                customUp: computed(() => {
                    const up = controls.up;
                    const threshold = 1e-6;
                    
                    // 检查是否为标准方向
                    const isStandardUp = this.vectorDistance(up, [0, 1, 0]) < threshold;
                    const isStandardDown = this.vectorDistance(up, [0, -1, 0]) < threshold;
                    const isStandardForward = this.vectorDistance(up, [0, 0, 1]) < threshold;
                    const isStandardBack = this.vectorDistance(up, [0, 0, -1]) < threshold;
                    
                    return !(isStandardUp || isStandardDown || isStandardForward || isStandardBack);
                })
            }),
            
            methods: {
                // 分辨率控制
                setResolution(resolution) {
                    this.resolution = resolution;
                    this.app.scale = resolution === 'low' ? 1 : Math.min(2, window.devicePixelRatio);
                    this.app.update();
                },
                
                // 全屏控制
                isFullscreenEnabled() {
                    return !!document.fullscreenEnabled;
                },
                
                toggleFullscreen() {
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    } else {
                        document.body.requestFullscreen();
                    }
                },
                
                // 上方向控制
                setUpDirection(direction) {
                    const directions = {
                        'up': [0, 1, 0],
                        'down': [0, -1, 0],
                        'forward': [0, 0, 1],
                        'back': [0, 0, -1]
                    };
                    
                    if (directions[direction]) {
                        controls.setUp(...directions[direction]);
                        this.app.update();
                    }
                },
                
                // 背景颜色控制
                setBackgroundColor(color) {
                    renderer.setBackgroundColor(color);
                    this.app.update();
                },
                
                // 重置视图
                resetView() {
                    controls.reset();
                },
                
                // 自动旋转
                toggleAutoRotate() {
                    if (controls.autoRotateSpeed > 0) {
                        controls.stopAutoRotate();
                    } else {
                        controls.autoRotate();
                    }
                },
                
                // 保存配置
                async saveConfiguration() {
                    if (!this.configMode) return;
                    
                    this.saving = true;
                    
                    try {
                        const message = await this.app.saveConfiguration();
                        this.showSuccessAlert(message || 'Configuration saved successfully');
                    } catch (error) {
                        this.showErrorAlert(`Save failed: ${error.message}`);
                    } finally {
                        this.saving = false;
                    }
                },
                
                // 复制视图参数
                copyViewParams() {
                    this.app.copyViewParams();
                    this.showSuccessAlert('View parameters copied to clipboard');
                },
                
                // 显示成功提示
                showSuccessAlert(message) {
                    this.alertMessage = message;
                    this.alertClass = 'alert-success show';
                    
                    setTimeout(() => {
                        this.alertClass = 'alert-success';
                    }, 3000);
                },
                
                // 显示错误提示
                showErrorAlert(message) {
                    this.alertMessage = message;
                    this.alertClass = 'alert-danger show';
                    
                    setTimeout(() => {
                        this.alertClass = 'alert-danger';
                    }, 5000);
                },
                
                // 向量距离计算
                vectorDistance(a, b) {
                    const dx = a[0] - b[0];
                    const dy = a[1] - b[1];
                    const dz = a[2] - b[2];
                    return Math.sqrt(dx * dx + dy * dy + dz * dz);
                }
            },
            
            watch: {
                // 监听分辨率变化
                resolution(newResolution) {
                    this.setResolution(newResolution);
                }
            },
            
            mounted() {
                // 监听全屏状态变化
                document.addEventListener('fullscreenchange', () => {
                    this.isFullscreen = !!document.fullscreenElement;
                });
            }
        });
        
        // 挂载Vue应用
        vueApp.mount('#viewer-ui');
        
        this.vueApp = vueApp;
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听指针按下事件，隐藏上方向滑块
        this.canvas.addEventListener('pointerdown', () => {
            const upSlider = document.getElementById('up-slider');
            if (upSlider) {
                upSlider.value = 0;
            }
        });
    }
    
    /**
     * 显示配置模式背景
     */
    showConfigBackground() {
        const style = document.body.style;
        style.backgroundImage = [
            'linear-gradient(to right, rgba(192, 192, 192, 0.75), rgba(192, 192, 192, 0.75))',
            'linear-gradient(to right, black 50%, white 50%)',
            'linear-gradient(to bottom, black 50%, white 50%)'
        ].join(', ');
        style.backgroundBlendMode = 'normal, difference, normal';
        style.backgroundSize = '2em 2em';
    }
    
    /**
     * 创建UI HTML结构
     */
    createUIHTML() {
        const uiHTML = `
            <div id="viewer-ui" class="viewer-ui">
                <!-- 主控制面板 -->
                <div class="control-panel" :class="{ 'config-mode': configMode }">
                    <!-- 分辨率控制 -->
                    <div class="control-group">
                        <label>Resolution:</label>
                        <select v-model="resolution" @change="setResolution(resolution)">
                            <option value="low">Low</option>
                            <option value="high">High</option>
                        </select>
                    </div>
                    
                    <!-- 上方向控制 -->
                    <div class="control-group" v-if="!isMobile">
                        <label>Up Direction:</label>
                        <div class="up-direction-controls">
                            <button @click="setUpDirection('up')" :class="{ active: !customUp && up[1] > 0 }">↑</button>
                            <button @click="setUpDirection('down')" :class="{ active: !customUp && up[1] < 0 }">↓</button>
                            <button @click="setUpDirection('forward')" :class="{ active: !customUp && up[2] > 0 }">→</button>
                            <button @click="setUpDirection('back')" :class="{ active: !customUp && up[2] < 0 }">←</button>
                        </div>
                        <div v-if="customUp" class="custom-up-info">
                            Custom: ({{ up[0].toFixed(2) }}, {{ up[1].toFixed(2) }}, {{ up[2].toFixed(2) }})
                        </div>
                    </div>
                    
                    <!-- 背景颜色控制 -->
                    <div class="control-group" v-if="configMode">
                        <label>Background:</label>
                        <input type="color" 
                               :value="rgbToHex(backgroundColor)" 
                               @input="setBackgroundColor(hexToRgb($event.target.value))">
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="control-group">
                        <button @click="resetView()" class="btn btn-secondary">Reset View</button>
                        <button @click="toggleAutoRotate()" class="btn btn-secondary">Auto Rotate</button>
                        <button @click="copyViewParams()" class="btn btn-secondary">Copy View</button>
                    </div>
                    
                    <!-- 全屏按钮 -->
                    <div class="control-group" v-if="isFullscreenEnabled()">
                        <button @click="toggleFullscreen()" class="btn btn-primary">
                            {{ isFullscreen ? 'Exit Fullscreen' : 'Fullscreen' }}
                        </button>
                    </div>
                    
                    <!-- 保存配置按钮 -->
                    <div class="control-group" v-if="configMode">
                        <button @click="saveConfiguration()" 
                                class="btn btn-success" 
                                :disabled="saving">
                            {{ saving ? 'Saving...' : 'Save Configuration' }}
                        </button>
                    </div>
                </div>
                
                <!-- 提示信息 -->
                <div class="alert" :class="alertClass" v-if="alertMessage">
                    {{ alertMessage }}
                </div>
                
                <!-- 加载指示器 -->
                <div class="loading-indicator" v-if="saving">
                    <div class="spinner"></div>
                    <span>Saving configuration...</span>
                </div>
            </div>
        `;
        
        // 将HTML插入到页面中
        document.body.insertAdjacentHTML('beforeend', uiHTML);
    }
    
    /**
     * 创建CSS样式
     */
    createUIStyles() {
        const styles = `
            <style>
                .viewer-ui {
                    position: fixed;
                    top: 0;
                    left: 0;
                    z-index: 1000;
                    pointer-events: none;
                }
                
                .control-panel {
                    position: absolute;
                    top: 20px;
                    right: 20px;
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    min-width: 200px;
                    pointer-events: auto;
                }
                
                .control-group {
                    margin-bottom: 15px;
                }
                
                .control-group:last-child {
                    margin-bottom: 0;
                }
                
                .control-group label {
                    display: block;
                    margin-bottom: 5px;
                    font-size: 12px;
                    text-transform: uppercase;
                    opacity: 0.8;
                }
                
                .control-group select,
                .control-group input {
                    width: 100%;
                    padding: 5px;
                    border: none;
                    border-radius: 4px;
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                }
                
                .up-direction-controls {
                    display: flex;
                    gap: 5px;
                }
                
                .up-direction-controls button {
                    flex: 1;
                    padding: 8px;
                    border: none;
                    border-radius: 4px;
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    cursor: pointer;
                }
                
                .up-direction-controls button.active {
                    background: #007bff;
                }
                
                .btn {
                    padding: 8px 12px;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    margin-right: 5px;
                    margin-bottom: 5px;
                }
                
                .btn-primary { background: #007bff; color: white; }
                .btn-secondary { background: #6c757d; color: white; }
                .btn-success { background: #28a745; color: white; }
                
                .btn:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
                
                .alert {
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    padding: 10px 20px;
                    border-radius: 4px;
                    opacity: 0;
                    transition: opacity 0.3s;
                    pointer-events: auto;
                }
                
                .alert.show {
                    opacity: 1;
                }
                
                .alert-success {
                    background: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                }
                
                .alert-danger {
                    background: #f8d7da;
                    color: #721c24;
                    border: 1px solid #f5c6cb;
                }
                
                .loading-indicator {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 20px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    pointer-events: auto;
                }
                
                .spinner {
                    width: 20px;
                    height: 20px;
                    border: 2px solid #ffffff33;
                    border-top: 2px solid white;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }
                
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                
                @media (max-width: 768px) {
                    .control-panel {
                        top: 10px;
                        right: 10px;
                        left: 10px;
                        min-width: auto;
                    }
                }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', styles);
    }
    
    /**
     * 初始化UI
     */
    init() {
        this.createUIStyles();
        this.createUIHTML();
    }
}
