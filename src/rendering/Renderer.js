/**
 * Splatter.app Viewer - WebGL Renderer
 * 核心渲染引擎，负责3D高斯散点的GPU渲染
 */

import { ShaderProgram } from './Shaders.js';
import { createTexture, createFramebuffer } from './Utils.js';

export class Renderer {
    constructor(gl, dataset) {
        this.gl = gl;
        this.dataset = dataset;
        
        // 渲染状态
        this.backgroundColor = [0, 0, 0, 1];
        this.onupdate = () => {};
        
        // WebGL资源
        this.shaderProgram = null;
        this.depthShaderProgram = null;
        this.framebuffer = null;
        this.textures = [];
        this.vertexBuffer = null;
        this.indexBuffer = null;
        
        // 渲染参数
        this.needsShaderRecompile = true;
        this.customEffect = '';
        this.customClip = '';
        this.customUniforms = {};
        this.uniformValues = {};
        
        // 初始化
        this.initializeGL();
        this.createShaders();
        this.createBuffers();
    }
    
    /**
     * 初始化WebGL状态
     */
    initializeGL() {
        const gl = this.gl;
        
        // 启用必要的WebGL功能
        gl.enable(gl.DEPTH_TEST);
        gl.enable(gl.BLEND);
        gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
        
        // 设置视口
        gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);
        
        // 创建高斯查找表纹理
        this.createGaussianLookupTexture();
    }
    
    /**
     * 创建高斯查找表纹理
     */
    createGaussianLookupTexture() {
        const gl = this.gl;
        const size = 256;
        const data = new Uint8Array(size * size);
        
        // 生成高斯分布查找表
        for (let y = 0; y < size; y++) {
            for (let x = 0; x < size; x++) {
                const u = x / (size - 1);
                const v = y / (size - 1);
                const r2 = u * u + v * v;
                const alpha = Math.exp(-r2 * 4); // 高斯函数
                data[y * size + x] = Math.floor(alpha * 255);
            }
        }
        
        this.gaussianTexture = createTexture(gl, size, size, data, gl.LUMINANCE);
    }
    
    /**
     * 创建着色器程序
     */
    createShaders() {
        if (!this.needsShaderRecompile) return;
        
        const gl = this.gl;
        
        // 清理旧的着色器
        if (this.shaderProgram) {
            this.shaderProgram.delete();
        }
        if (this.depthShaderProgram) {
            this.depthShaderProgram.delete();
        }
        
        // 生成着色器代码
        let defines = '';
        
        if (this.customEffect) {
            defines += `#define CUSTOM_EFFECT ${this.customEffect.replace(/\/\/.*\n/g, '').replace(/\n/g, '')}\n`;
        }
        
        if (this.customClip) {
            defines += `#define CUSTOM_CLIP ${this.customClip.replace(/\/\/.*\n/g, '').replace(/\n/g, '')}\n`;
        }
        
        if (Object.keys(this.customUniforms).length > 0) {
            defines += '#define CUSTOM_UNIFORMS ';
            for (const [name, type] of Object.entries(this.customUniforms)) {
                defines += `uniform ${type} ${name}; `;
            }
            defines += '\n';
        }
        
        // 创建主渲染着色器
        this.shaderProgram = new ShaderProgram(gl, this.getVertexShader(), this.getFragmentShader(), defines);
        
        // 创建深度渲染着色器
        const depthDefines = defines + '#define RENDER_DEPTH\n';
        this.depthShaderProgram = new ShaderProgram(gl, this.getVertexShader(), this.getFragmentShader(), depthDefines);
        
        this.needsShaderRecompile = false;
    }
    
    /**
     * 获取顶点着色器代码
     */
    getVertexShader() {
        return `#version 300 es
            precision highp float;
            
            in vec3 position;
            in vec4 color;
            in vec3 scale;
            in vec4 rotation;
            in float opacity;
            
            uniform mat4 viewMatrix;
            uniform mat4 projectionMatrix;
            uniform vec2 viewport;
            uniform float focalX;
            uniform float focalY;
            
            out vec4 vColor;
            out vec2 vUV;
            
            // 四元数旋转函数
            vec3 rotateByQuaternion(vec3 v, vec4 q) {
                vec3 qvec = q.xyz;
                vec3 uv = cross(qvec, v);
                vec3 uuv = cross(qvec, uv);
                return v + 2.0 * (uv * q.w + uuv);
            }
            
            void main() {
                // 计算世界空间位置
                vec4 worldPos = vec4(position, 1.0);
                vec4 viewPos = viewMatrix * worldPos;
                
                // 计算椭球体在屏幕空间的投影
                mat3 rotMat = mat3(1.0); // 从四元数构建旋转矩阵
                mat3 scaleMat = mat3(
                    scale.x, 0.0, 0.0,
                    0.0, scale.y, 0.0,
                    0.0, 0.0, scale.z
                );
                
                mat3 covariance = rotMat * scaleMat * transpose(rotMat);
                
                // 投影到屏幕空间
                float z = -viewPos.z;
                mat2 J = mat2(
                    focalX / z, 0.0,
                    0.0, focalY / z
                );
                
                mat2 screenCov = J * mat2(covariance) * transpose(J);
                
                // 计算椭圆的半轴
                float det = screenCov[0][0] * screenCov[1][1] - screenCov[0][1] * screenCov[1][0];
                float trace = screenCov[0][0] + screenCov[1][1];
                float discriminant = trace * trace - 4.0 * det;
                
                if (discriminant < 0.0) {
                    gl_Position = vec4(0.0, 0.0, -1.0, 1.0);
                    return;
                }
                
                float sqrtDisc = sqrt(discriminant);
                float eigenval1 = 0.5 * (trace + sqrtDisc);
                float eigenval2 = 0.5 * (trace - sqrtDisc);
                
                float radius = 3.0 * sqrt(max(eigenval1, eigenval2));
                
                // 生成四边形顶点
                vec2 quadPos = vec2(
                    (gl_VertexID % 2) * 2.0 - 1.0,
                    (gl_VertexID / 2) * 2.0 - 1.0
                ) * radius;
                
                vec4 clipPos = projectionMatrix * viewPos;
                clipPos.xy += quadPos / viewport * clipPos.w;
                
                gl_Position = clipPos;
                
                // 传递给片段着色器的数据
                vColor = vec4(color.rgb, opacity);
                vUV = quadPos / radius;
            }
        `;
    }
    
    /**
     * 获取片段着色器代码
     */
    getFragmentShader() {
        return `#version 300 es
            precision mediump float;
            
            uniform vec4 options;
            uniform lowp sampler2D gtable;
            
            in lowp vec4 vColor;
            in vec2 vUV;
            
            out vec4 fragColor;
            
            void main() {
                float distSq = dot(vUV, vUV);
                if (distSq > 4.0) discard;
                
                #ifdef CUSTOM_CLIP
                CUSTOM_CLIP
                #endif
                
                // 使用高斯查找表
                float alpha = min(vColor.a, 1.0) * max(
                    texture(gtable, vec2(0.25 * distSq, vColor.a - 1.0)).r,
                    options.x
                );
                
                #ifdef CUSTOM_EFFECT
                CUSTOM_EFFECT
                #endif
                
                #ifndef RENDER_DEPTH
                fragColor = vec4(vColor.rgb * alpha, alpha);
                #else
                if (alpha < 0.25) discard;
                fragColor = vec4(vColor.rgb, 1.0);
                #endif
            }
        `;
    }
    
    /**
     * 创建缓冲区
     */
    createBuffers() {
        const gl = this.gl;
        
        // 创建顶点缓冲区
        this.vertexBuffer = gl.createBuffer();
        
        // 创建索引缓冲区
        this.indexBuffer = gl.createBuffer();
        const indices = new Uint16Array([0, 1, 2, 0, 2, 3]);
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
        gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.STATIC_DRAW);
    }
    
    /**
     * 主渲染函数
     */
    render(camera, width, height) {
        const gl = this.gl;
        
        // 更新视口
        gl.viewport(0, 0, width, height);
        
        // 清空画布
        gl.clearColor(...this.backgroundColor);
        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
        
        // 重新编译着色器（如果需要）
        this.createShaders();
        
        // 使用着色器程序
        this.shaderProgram.use();
        
        // 设置uniform变量
        this.shaderProgram.setUniform('viewMatrix', camera.viewMatrix);
        this.shaderProgram.setUniform('projectionMatrix', camera.projectionMatrix);
        this.shaderProgram.setUniform('viewport', [width, height]);
        this.shaderProgram.setUniform('focalX', camera.focalLength * width * 0.5);
        this.shaderProgram.setUniform('focalY', camera.focalLength * height * 0.5);
        
        // 绑定高斯查找表纹理
        gl.activeTexture(gl.TEXTURE0);
        gl.bindTexture(gl.TEXTURE_2D, this.gaussianTexture);
        this.shaderProgram.setUniform('gtable', 0);
        
        // 设置自定义uniform
        for (const [name, value] of Object.entries(this.uniformValues)) {
            this.shaderProgram.setUniform(name, value);
        }
        
        // 渲染散点数据
        this.renderSplats();
    }
    
    /**
     * 渲染散点数据
     */
    renderSplats() {
        // 这里需要根据实际的数据格式来实现
        // 通常会遍历所有可见的散点块并渲染
    }
    
    /**
     * 设置背景颜色
     */
    setBackgroundColor(color) {
        this.backgroundColor = color;
    }
    
    /**
     * 设置自定义效果
     */
    setCustomEffect(code) {
        this.customEffect = code;
        this.needsShaderRecompile = true;
    }
    
    /**
     * 设置自定义裁剪
     */
    setCustomClip(code) {
        this.customClip = code;
        this.needsShaderRecompile = true;
    }
    
    /**
     * 添加自定义uniform
     */
    addCustomUniform(name, type) {
        this.customUniforms[name] = type;
        this.uniformValues[name] = null;
        this.needsShaderRecompile = true;
    }
    
    /**
     * 设置uniform值
     */
    setUniformValue(name, value) {
        this.uniformValues[name] = value;
    }
    
    /**
     * 获取指定位置的深度值
     */
    getDepthAt(camera, positions) {
        // 实现深度查询逻辑
        return positions.map(() => 1.0);
    }
    
    /**
     * 清理资源
     */
    cleanup() {
        const gl = this.gl;
        
        if (this.shaderProgram) {
            this.shaderProgram.delete();
        }
        if (this.depthShaderProgram) {
            this.depthShaderProgram.delete();
        }
        if (this.vertexBuffer) {
            gl.deleteBuffer(this.vertexBuffer);
        }
        if (this.indexBuffer) {
            gl.deleteBuffer(this.indexBuffer);
        }
        if (this.gaussianTexture) {
            gl.deleteTexture(this.gaussianTexture);
        }
        
        this.textures.forEach(texture => gl.deleteTexture(texture));
        
        if (this.framebuffer) {
            gl.deleteFramebuffer(this.framebuffer);
        }
    }
}
