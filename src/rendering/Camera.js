/**
 * Splatter.app Viewer - Camera System
 * 相机系统，处理视图变换和投影
 */

import { mat4, vec3, quat } from '../utils/Math.js';

export class Camera {
    constructor(fov = 50) {
        this.fov = fov;
        this.aspectRatio = 1.0;
        this.near = 0.1;
        this.far = 1000.0;
        
        // 相机位置和方向
        this.position = vec3.create();
        this.target = vec3.create();
        this.up = vec3.fromValues(0, 1, 0);
        
        // 变换矩阵
        this.viewMatrix = mat4.create();
        this.projectionMatrix = mat4.create();
        this.viewProjectionMatrix = mat4.create();
        this.inverseViewMatrix = mat4.create();
        this.inverseProjectionMatrix = mat4.create();
        
        // 焦距
        this.focalLength = 1.0;
        
        // 初始化
        this.updateMatrices();
    }
    
    /**
     * 设置视野角度
     */
    setFOV(fov) {
        this.fov = fov;
        this.updateProjectionMatrix();
    }
    
    /**
     * 设置宽高比
     */
    setAspectRatio(aspectRatio) {
        this.aspectRatio = aspectRatio;
        this.updateProjectionMatrix();
    }
    
    /**
     * 设置相机位置
     */
    setPosition(x, y, z) {
        vec3.set(this.position, x, y, z);
        this.updateViewMatrix();
    }
    
    /**
     * 设置目标点
     */
    setTarget(x, y, z) {
        vec3.set(this.target, x, y, z);
        this.updateViewMatrix();
    }
    
    /**
     * 设置上方向
     */
    setUp(x, y, z) {
        vec3.set(this.up, x, y, z);
        vec3.normalize(this.up, this.up);
        this.updateViewMatrix();
    }
    
    /**
     * 从球坐标设置相机
     */
    setFromSpherical(center, radius, azimuth, elevation) {
        const phi = (90 - elevation) * Math.PI / 180;
        const theta = azimuth * Math.PI / 180;
        
        const x = center[0] + radius * Math.sin(phi) * Math.cos(theta);
        const y = center[1] + radius * Math.cos(phi);
        const z = center[2] + radius * Math.sin(phi) * Math.sin(theta);
        
        this.setPosition(x, y, z);
        this.setTarget(center[0], center[1], center[2]);
    }
    
    /**
     * 更新视图矩阵
     */
    updateViewMatrix() {
        mat4.lookAt(this.viewMatrix, this.position, this.target, this.up);
        mat4.invert(this.inverseViewMatrix, this.viewMatrix);
        this.updateViewProjectionMatrix();
    }
    
    /**
     * 更新投影矩阵
     */
    updateProjectionMatrix() {
        const fovRad = this.fov * Math.PI / 180;
        mat4.perspective(this.projectionMatrix, fovRad, this.aspectRatio, this.near, this.far);
        mat4.invert(this.inverseProjectionMatrix, this.projectionMatrix);
        
        // 计算焦距
        this.focalLength = 1.0 / Math.tan(fovRad * 0.5);
        
        this.updateViewProjectionMatrix();
    }
    
    /**
     * 更新视图投影矩阵
     */
    updateViewProjectionMatrix() {
        mat4.multiply(this.viewProjectionMatrix, this.projectionMatrix, this.viewMatrix);
    }
    
    /**
     * 更新所有矩阵
     */
    updateMatrices() {
        this.updateViewMatrix();
        this.updateProjectionMatrix();
    }
    
    /**
     * 获取相机前方向量
     */
    getForward() {
        const forward = vec3.create();
        vec3.subtract(forward, this.target, this.position);
        vec3.normalize(forward, forward);
        return forward;
    }
    
    /**
     * 获取相机右方向量
     */
    getRight() {
        const forward = this.getForward();
        const right = vec3.create();
        vec3.cross(right, forward, this.up);
        vec3.normalize(right, right);
        return right;
    }
    
    /**
     * 获取相机上方向量
     */
    getUp() {
        const forward = this.getForward();
        const right = this.getRight();
        const up = vec3.create();
        vec3.cross(up, right, forward);
        vec3.normalize(up, up);
        return up;
    }
    
    /**
     * 屏幕坐标转世界射线
     */
    screenToWorldRay(screenX, screenY) {
        // 标准化设备坐标
        const ndc = vec3.fromValues(
            screenX * 2 - 1,
            (1 - screenY) * 2 - 1,
            -1
        );
        
        // 转换到视图空间
        const viewPos = vec3.create();
        vec3.transformMat4(viewPos, ndc, this.inverseProjectionMatrix);
        viewPos[2] = -1;
        
        // 转换到世界空间
        const worldPos = vec3.create();
        vec3.transformMat4(worldPos, viewPos, this.inverseViewMatrix);
        
        // 计算射线方向
        const direction = vec3.create();
        vec3.subtract(direction, worldPos, this.position);
        vec3.normalize(direction, direction);
        
        return {
            origin: vec3.clone(this.position),
            direction: direction
        };
    }
    
    /**
     * 射线投射
     */
    raycast(screenX, screenY, depth) {
        const ray = this.screenToWorldRay(screenX, screenY);
        
        // 如果有深度信息，计算交点
        if (depth !== undefined && depth > 0) {
            const point = vec3.create();
            vec3.scaleAndAdd(point, ray.origin, ray.direction, depth);
            return point;
        }
        
        return ray;
    }
    
    /**
     * 世界坐标转屏幕坐标
     */
    worldToScreen(worldPos) {
        const clipPos = vec3.create();
        vec3.transformMat4(clipPos, worldPos, this.viewProjectionMatrix);
        
        // 透视除法
        if (clipPos[3] !== 0) {
            clipPos[0] /= clipPos[3];
            clipPos[1] /= clipPos[3];
            clipPos[2] /= clipPos[3];
        }
        
        // 转换到屏幕坐标
        const screenX = (clipPos[0] + 1) * 0.5;
        const screenY = (1 - clipPos[1]) * 0.5;
        
        return [screenX, screenY, clipPos[2]];
    }
    
    /**
     * 检查点是否在视锥体内
     */
    isPointInFrustum(point) {
        const clipPos = vec3.create();
        vec3.transformMat4(clipPos, point, this.viewProjectionMatrix);
        
        // 检查是否在视锥体内
        return (
            clipPos[0] >= -clipPos[3] && clipPos[0] <= clipPos[3] &&
            clipPos[1] >= -clipPos[3] && clipPos[1] <= clipPos[3] &&
            clipPos[2] >= -clipPos[3] && clipPos[2] <= clipPos[3]
        );
    }
    
    /**
     * 计算到点的距离
     */
    distanceToPoint(point) {
        return vec3.distance(this.position, point);
    }
    
    /**
     * 获取视锥体平面
     */
    getFrustumPlanes() {
        const planes = [];
        const m = this.viewProjectionMatrix;
        
        // 左平面
        planes.push([
            m[3] + m[0],
            m[7] + m[4],
            m[11] + m[8],
            m[15] + m[12]
        ]);
        
        // 右平面
        planes.push([
            m[3] - m[0],
            m[7] - m[4],
            m[11] - m[8],
            m[15] - m[12]
        ]);
        
        // 下平面
        planes.push([
            m[3] + m[1],
            m[7] + m[5],
            m[11] + m[9],
            m[15] + m[13]
        ]);
        
        // 上平面
        planes.push([
            m[3] - m[1],
            m[7] - m[5],
            m[11] - m[9],
            m[15] - m[13]
        ]);
        
        // 近平面
        planes.push([
            m[3] + m[2],
            m[7] + m[6],
            m[11] + m[10],
            m[15] + m[14]
        ]);
        
        // 远平面
        planes.push([
            m[3] - m[2],
            m[7] - m[6],
            m[11] - m[10],
            m[15] - m[14]
        ]);
        
        // 标准化平面
        return planes.map(plane => {
            const length = Math.sqrt(plane[0] * plane[0] + plane[1] * plane[1] + plane[2] * plane[2]);
            return [plane[0] / length, plane[1] / length, plane[2] / length, plane[3] / length];
        });
    }
    
    /**
     * 复制相机状态
     */
    copy(other) {
        this.fov = other.fov;
        this.aspectRatio = other.aspectRatio;
        this.near = other.near;
        this.far = other.far;
        
        vec3.copy(this.position, other.position);
        vec3.copy(this.target, other.target);
        vec3.copy(this.up, other.up);
        
        this.updateMatrices();
    }
    
    /**
     * 克隆相机
     */
    clone() {
        const camera = new Camera(this.fov);
        camera.copy(this);
        return camera;
    }
}
