/**
 * Splatter.app Viewer - Controls System
 * 用户交互控制系统，处理鼠标、触摸和键盘输入
 */

import { vec3, quat } from '../utils/Math.js';
import { isMobile } from '../utils/Utils.js';

export class Controls {
    constructor(canvas) {
        this.canvas = canvas;
        
        // 控制状态
        this.enabled = true;
        this.center = [0, 0, 0];
        this.radius = 5.0;
        this.azimuth = 0;
        this.elevation = 0;
        this.up = [0, 1, 0];
        
        // 交互状态
        this.isPointerDown = false;
        this.pointerType = 'mouse';
        this.lastPointerX = 0;
        this.lastPointerY = 0;
        this.pointerStartX = 0;
        this.pointerStartY = 0;
        
        // 触摸状态
        this.touches = new Map();
        this.lastTouchDistance = 0;
        this.lastTouchCenter = [0, 0];
        
        // 动画状态
        this.isAnimating = false;
        this.autoRotateSpeed = 0;
        this.dampingFactor = 0.1;
        this.lastUpdateTime = 0;
        
        // 约束
        this.minRadius = 0.1;
        this.maxRadius = 100;
        this.minElevation = -89;
        this.maxElevation = 89;
        
        // 回调
        this.onupdate = () => {};
        this.raycast = null;
        
        // 初始化事件监听
        this.setupEventListeners();
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        const canvas = this.canvas;
        
        // 鼠标事件
        canvas.addEventListener('pointerdown', this.onPointerDown.bind(this));
        canvas.addEventListener('pointermove', this.onPointerMove.bind(this));
        canvas.addEventListener('pointerup', this.onPointerUp.bind(this));
        canvas.addEventListener('wheel', this.onWheel.bind(this));
        
        // 触摸事件
        canvas.addEventListener('touchstart', this.onTouchStart.bind(this));
        canvas.addEventListener('touchmove', this.onTouchMove.bind(this));
        canvas.addEventListener('touchend', this.onTouchEnd.bind(this));
        
        // 防止上下文菜单
        canvas.addEventListener('contextmenu', (e) => e.preventDefault());
        
        // 键盘事件
        document.addEventListener('keydown', this.onKeyDown.bind(this));
    }
    
    /**
     * 指针按下事件
     */
    onPointerDown(event) {
        if (!this.enabled) return;
        
        this.isPointerDown = true;
        this.pointerType = event.pointerType;
        this.lastPointerX = this.pointerStartX = event.clientX;
        this.lastPointerY = this.pointerStartY = event.clientY;
        
        this.canvas.setPointerCapture(event.pointerId);
        this.stopAutoRotate();
        
        event.preventDefault();
    }
    
    /**
     * 指针移动事件
     */
    onPointerMove(event) {
        if (!this.enabled || !this.isPointerDown) return;
        
        const deltaX = event.clientX - this.lastPointerX;
        const deltaY = event.clientY - this.lastPointerY;
        
        if (event.buttons === 1) {
            // 左键：旋转
            this.rotate(deltaX, deltaY);
        } else if (event.buttons === 2) {
            // 右键：平移
            this.pan(deltaX, deltaY);
        }
        
        this.lastPointerX = event.clientX;
        this.lastPointerY = event.clientY;
        
        this.onupdate();
        event.preventDefault();
    }
    
    /**
     * 指针释放事件
     */
    onPointerUp(event) {
        if (!this.enabled) return;
        
        this.isPointerDown = false;
        this.canvas.releasePointerCapture(event.pointerId);
        
        // 检查是否是点击（而不是拖拽）
        const deltaX = Math.abs(event.clientX - this.pointerStartX);
        const deltaY = Math.abs(event.clientY - this.pointerStartY);
        
        if (deltaX < 5 && deltaY < 5) {
            this.onClick(event);
        }
        
        event.preventDefault();
    }
    
    /**
     * 滚轮事件
     */
    onWheel(event) {
        if (!this.enabled) return;
        
        const delta = event.deltaY > 0 ? 1.1 : 0.9;
        this.zoom(delta);
        
        this.onupdate();
        event.preventDefault();
    }
    
    /**
     * 触摸开始事件
     */
    onTouchStart(event) {
        if (!this.enabled) return;
        
        this.updateTouches(event);
        
        if (this.touches.size === 1) {
            // 单指触摸：准备旋转
            const touch = this.touches.values().next().value;
            this.lastPointerX = touch.clientX;
            this.lastPointerY = touch.clientY;
        } else if (this.touches.size === 2) {
            // 双指触摸：准备缩放和平移
            this.updateTouchGesture();
        }
        
        this.stopAutoRotate();
        event.preventDefault();
    }
    
    /**
     * 触摸移动事件
     */
    onTouchMove(event) {
        if (!this.enabled) return;
        
        this.updateTouches(event);
        
        if (this.touches.size === 1) {
            // 单指旋转
            const touch = this.touches.values().next().value;
            const deltaX = touch.clientX - this.lastPointerX;
            const deltaY = touch.clientY - this.lastPointerY;
            
            this.rotate(deltaX, deltaY);
            
            this.lastPointerX = touch.clientX;
            this.lastPointerY = touch.clientY;
        } else if (this.touches.size === 2) {
            // 双指缩放和平移
            this.handleTouchGesture();
        }
        
        this.onupdate();
        event.preventDefault();
    }
    
    /**
     * 触摸结束事件
     */
    onTouchEnd(event) {
        if (!this.enabled) return;
        
        this.updateTouches(event);
        
        if (this.touches.size === 0) {
            // 所有触摸结束
            this.lastTouchDistance = 0;
        } else if (this.touches.size === 1) {
            // 从双指变为单指
            const touch = this.touches.values().next().value;
            this.lastPointerX = touch.clientX;
            this.lastPointerY = touch.clientY;
        }
        
        event.preventDefault();
    }
    
    /**
     * 更新触摸点信息
     */
    updateTouches(event) {
        this.touches.clear();
        for (let i = 0; i < event.touches.length; i++) {
            const touch = event.touches[i];
            this.touches.set(touch.identifier, touch);
        }
    }
    
    /**
     * 更新触摸手势信息
     */
    updateTouchGesture() {
        if (this.touches.size !== 2) return;
        
        const touches = Array.from(this.touches.values());
        const touch1 = touches[0];
        const touch2 = touches[1];
        
        // 计算距离
        const dx = touch2.clientX - touch1.clientX;
        const dy = touch2.clientY - touch1.clientY;
        this.lastTouchDistance = Math.sqrt(dx * dx + dy * dy);
        
        // 计算中心点
        this.lastTouchCenter = [
            (touch1.clientX + touch2.clientX) * 0.5,
            (touch1.clientY + touch2.clientY) * 0.5
        ];
    }
    
    /**
     * 处理触摸手势
     */
    handleTouchGesture() {
        if (this.touches.size !== 2) return;
        
        const touches = Array.from(this.touches.values());
        const touch1 = touches[0];
        const touch2 = touches[1];
        
        // 计算当前距离和中心点
        const dx = touch2.clientX - touch1.clientX;
        const dy = touch2.clientY - touch1.clientY;
        const currentDistance = Math.sqrt(dx * dx + dy * dy);
        const currentCenter = [
            (touch1.clientX + touch2.clientX) * 0.5,
            (touch1.clientY + touch2.clientY) * 0.5
        ];
        
        if (this.lastTouchDistance > 0) {
            // 缩放
            const scale = currentDistance / this.lastTouchDistance;
            this.zoom(1 / scale);
            
            // 平移
            const panX = currentCenter[0] - this.lastTouchCenter[0];
            const panY = currentCenter[1] - this.lastTouchCenter[1];
            this.pan(panX, panY);
        }
        
        this.lastTouchDistance = currentDistance;
        this.lastTouchCenter = currentCenter;
    }
    
    /**
     * 键盘事件
     */
    onKeyDown(event) {
        if (!this.enabled) return;
        
        const speed = 0.1;
        
        switch (event.code) {
            case 'ArrowLeft':
                this.rotate(-10, 0);
                break;
            case 'ArrowRight':
                this.rotate(10, 0);
                break;
            case 'ArrowUp':
                this.rotate(0, -10);
                break;
            case 'ArrowDown':
                this.rotate(0, 10);
                break;
            case 'Equal':
            case 'NumpadAdd':
                this.zoom(0.9);
                break;
            case 'Minus':
            case 'NumpadSubtract':
                this.zoom(1.1);
                break;
        }
        
        this.onupdate();
    }
    
    /**
     * 点击事件
     */
    onClick(event) {
        if (this.raycast) {
            const rect = this.canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            const result = this.raycast(x, y);
            if (result) {
                // 可以在这里处理点击结果
                console.log('Clicked at:', result);
            }
        }
    }
    
    /**
     * 旋转控制
     */
    rotate(deltaX, deltaY) {
        const sensitivity = 0.5;
        
        this.azimuth -= deltaX * sensitivity;
        this.elevation = Math.max(
            this.minElevation,
            Math.min(this.maxElevation, this.elevation - deltaY * sensitivity)
        );
        
        // 标准化方位角
        this.azimuth = ((this.azimuth % 360) + 360) % 360;
    }
    
    /**
     * 平移控制
     */
    pan(deltaX, deltaY) {
        const sensitivity = 0.001 * this.radius;
        
        // 计算相机的右方向和上方向
        const azimuthRad = this.azimuth * Math.PI / 180;
        const elevationRad = this.elevation * Math.PI / 180;
        
        const right = [
            Math.cos(azimuthRad + Math.PI / 2),
            0,
            Math.sin(azimuthRad + Math.PI / 2)
        ];
        
        const up = vec3.cross([], this.up, right);
        vec3.normalize(up, up);
        
        // 应用平移
        this.center[0] -= right[0] * deltaX * sensitivity + up[0] * deltaY * sensitivity;
        this.center[1] -= right[1] * deltaX * sensitivity + up[1] * deltaY * sensitivity;
        this.center[2] -= right[2] * deltaX * sensitivity + up[2] * deltaY * sensitivity;
    }
    
    /**
     * 缩放控制
     */
    zoom(factor) {
        this.radius = Math.max(
            this.minRadius,
            Math.min(this.maxRadius, this.radius * factor)
        );
    }
    
    /**
     * 设置上方向
     */
    setUp(upDirection) {
        this.up = [...upDirection];
        vec3.normalize(this.up, this.up);
    }
    
    /**
     * 设置默认视图
     */
    setDefaultView(viewParams) {
        if (viewParams && viewParams.length >= 6) {
            this.center = [viewParams[0], viewParams[1], viewParams[2]];
            this.azimuth = viewParams[3] + 90;
            this.elevation = viewParams[4];
            this.radius = Math.pow(2, viewParams[5]);
        }
    }
    
    /**
     * 获取视图参数
     */
    getViewParams() {
        return [
            this.center[0], this.center[1], this.center[2],
            this.azimuth - 90, this.elevation,
            Math.log2(this.radius)
        ];
    }
    
    /**
     * 重置到默认视图
     */
    reset() {
        this.center = [0, 0, 0];
        this.radius = 5.0;
        this.azimuth = 0;
        this.elevation = 0;
        this.onupdate();
    }
    
    /**
     * 开始自动旋转
     */
    autoRotate() {
        this.autoRotateSpeed = 0.5;
        this.isAnimating = true;
    }
    
    /**
     * 停止自动旋转
     */
    stopAutoRotate() {
        this.autoRotateSpeed = 0;
        this.isAnimating = false;
    }
    
    /**
     * 更新控制器状态
     */
    update() {
        if (this.autoRotateSpeed > 0) {
            const now = performance.now();
            const deltaTime = (now - this.lastUpdateTime) / 1000;
            this.lastUpdateTime = now;
            
            this.azimuth += this.autoRotateSpeed * deltaTime * 10;
            this.onupdate();
        }
    }
    
    /**
     * 获取相机位置
     */
    getCameraPosition() {
        const azimuthRad = this.azimuth * Math.PI / 180;
        const elevationRad = this.elevation * Math.PI / 180;
        
        const x = this.center[0] + this.radius * Math.cos(elevationRad) * Math.cos(azimuthRad);
        const y = this.center[1] + this.radius * Math.sin(elevationRad);
        const z = this.center[2] + this.radius * Math.cos(elevationRad) * Math.sin(azimuthRad);
        
        return [x, y, z];
    }
    
    /**
     * 启用/禁用控制器
     */
    setEnabled(enabled) {
        this.enabled = enabled;
    }
}
