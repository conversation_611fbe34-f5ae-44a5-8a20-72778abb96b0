# Splatter.app Viewer - 架构分析与复原

## 概述
Splatter.app Viewer 是一个基于WebGL2的3D高斯散点(Gaussian Splatting)渲染器，用于实时显示和交互3D场景。该应用采用现代Web技术栈，支持高性能实时渲染。

## 核心架构组件

### 1. 主应用类 (Main Application - class X)
- **职责**: 应用程序的主入口点和协调器
- **功能**:
  - 初始化所有子系统
  - 管理应用生命周期
  - 处理窗口事件和键盘输入
  - 协调各组件间的通信

### 2. 数据加载器 (Data Loader - class B)
- **职责**: 管理3D散点数据的加载和流式传输
- **功能**:
  - 从服务器获取元数据 (`/meta` 端点)
  - 分块加载散点数据
  - 实现数据缓存和预加载机制
  - 支持多线程数据处理

### 3. WebGL渲染器 (Renderer - class j)
- **职责**: 核心渲染引擎
- **功能**:
  - WebGL2上下文管理
  - 着色器程序编译和管理
  - 纹理和帧缓冲管理
  - 实现高斯散点渲染算法
  - 支持自定义着色器效果

### 4. 相机系统 (Camera - class J)
- **职责**: 视图变换和投影管理
- **功能**:
  - 透视投影计算
  - 视图矩阵变换
  - 视锥体裁剪
  - 射线投射(Ray casting)

### 5. 控制系统 (Controls - class x)
- **职责**: 用户交互处理
- **功能**:
  - 鼠标/触摸输入处理
  - 相机轨道控制
  - 缩放和平移
  - 手势识别(移动设备)

### 6. UI管理器 (UI Manager - class p)
- **职责**: 用户界面管理
- **技术栈**: Vue.js 3
- **功能**:
  - 响应式UI组件
  - 设置面板
  - 全屏控制
  - 移动设备适配

### 7. 工作线程管理器 (Worker Manager - function A)
- **职责**: 多线程处理协调
- **功能**:
  - 创建和管理Web Workers
  - 数据处理任务分发
  - 线程间通信

## 技术特性

### 渲染技术
- **3D高斯散点渲染**: 实现高质量的点云可视化
- **实时渲染**: 60fps流畅体验
- **LOD系统**: 根据距离调整细节级别
- **遮挡剔除**: 优化渲染性能

### 数据管理
- **流式加载**: 支持大规模数据集
- **分块缓存**: 智能数据预加载
- **压缩格式**: 优化网络传输

### 用户体验
- **响应式设计**: 适配各种屏幕尺寸
- **触摸支持**: 移动设备友好
- **全屏模式**: 沉浸式体验
- **配置模式**: 开发者工具

## 数据流架构

```
用户输入 → 控制系统 → 相机系统 → 渲染器
    ↓           ↓          ↓         ↓
UI管理器 ← 主应用 ← 数据加载器 ← 工作线程
```

## 文件结构 (复原)

```
src/
├── core/
│   ├── Application.js      # 主应用类
│   ├── DataLoader.js       # 数据加载器
│   └── WorkerManager.js    # 工作线程管理
├── rendering/
│   ├── Renderer.js         # WebGL渲染器
│   ├── Camera.js           # 相机系统
│   ├── Shaders.js          # 着色器定义
│   └── Utils.js            # 渲染工具函数
├── controls/
│   └── Controls.js         # 用户控制
├── ui/
│   └── UIManager.js        # Vue.js UI管理
└── utils/
    ├── Math.js             # 数学工具
    └── Constants.js        # 常量定义
```

## 关键算法

### 1. 高斯散点渲染
- 使用椭球体表示每个散点
- 实时计算散点的屏幕投影
- 基于深度的alpha混合

### 2. 视锥体裁剪
- 层次化包围盒检测
- 动态LOD调整
- 遮挡查询优化

### 3. 数据流式加载
- 基于视点的优先级排序
- 预测性数据预加载
- 内存使用优化

## 性能优化策略

1. **GPU加速**: 充分利用WebGL2特性
2. **多线程处理**: Web Workers并行计算
3. **内存管理**: 智能缓存和垃圾回收
4. **网络优化**: 数据压缩和分块传输
5. **渲染优化**: 批处理和实例化渲染

## API接口

### 数据端点
- `GET /{splatId}/meta` - 获取场景元数据
- `GET /{splatId}/data/{blockId}` - 获取数据块
- `POST /api/splat/configure` - 配置场景参数

### URL参数
- `id`: 场景ID
- `src`: 数据源路径
- `fov`: 视野角度
- `configure`: 配置模式令牌

## 浏览器兼容性
- 要求WebGL2支持
- 现代浏览器(Chrome 56+, Firefox 51+, Safari 15+)
- 移动设备优化

## 开发工具
- 配置模式: 场景参数调试
- 性能监控: 帧率和内存使用
- 开发者控制台: 调试信息输出
